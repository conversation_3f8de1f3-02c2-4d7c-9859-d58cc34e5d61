%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12004, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: UnityEditor.dll::UnityEditor.ContainerWindow
  m_PixelRect:
    serializedVersion: 2
    x: 1048
    y: 51
    width: 2060
    height: 1388
  m_ShowMode: 4
  m_Title: Hierarchy
  m_RootView: {fileID: 2}
  m_MinSize: {x: 875, y: 300}
  m_MaxSize: {x: 10000, y: 10000}
  m_Maximized: 0
--- !u!114 &2
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12008, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 3}
  - {fileID: 5}
  - {fileID: 4}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 2060
    height: 1388
  m_MinSize: {x: 875, y: 300}
  m_MaxSize: {x: 10000, y: 10000}
  m_UseTopView: 1
  m_TopViewHeight: 36
  m_UseBottomView: 1
  m_BottomViewHeight: 20
--- !u!114 &3
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12011, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 2060
    height: 36
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
  m_LastLoadedLayoutName: 
--- !u!114 &4
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12042, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 1368
    width: 2060
    height: 20
  m_MinSize: {x: 0, y: 0}
  m_MaxSize: {x: 0, y: 0}
--- !u!114 &5
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 6}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 36
    width: 2060
    height: 1332
  m_MinSize: {x: 400, y: 100}
  m_MaxSize: {x: 32384, y: 16192}
  vertical: 1
  controlID: 188
  draggingID: 0
--- !u!114 &6
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 7}
  - {fileID: 15}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 2060
    height: 1332
  m_MinSize: {x: 400, y: 100}
  m_MaxSize: {x: 32384, y: 16192}
  vertical: 0
  controlID: 189
  draggingID: 0
--- !u!114 &7
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 8}
  - {fileID: 11}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1633
    height: 1332
  m_MinSize: {x: 300, y: 100}
  m_MaxSize: {x: 24288, y: 16192}
  vertical: 1
  controlID: 158
  draggingID: 0
--- !u!114 &8
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 9}
  - {fileID: 10}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1633
    height: 1011
  m_MinSize: {x: 200, y: 50}
  m_MaxSize: {x: 16192, y: 8096}
  vertical: 0
  controlID: 67
  draggingID: 0
--- !u!114 &9
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: SceneHierarchyWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 248
    height: 1011
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_ActualView: {fileID: 18}
  m_Panes:
  - {fileID: 17}
  - {fileID: 18}
  m_Selected: 1
  m_LastSelected: 0
--- !u!114 &10
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: GameView
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 248
    y: 0
    width: 1385
    height: 1011
  m_MinSize: {x: 52, y: 76}
  m_MaxSize: {x: 4002, y: 4026}
  m_ActualView: {fileID: 16}
  m_Panes:
  - {fileID: 19}
  - {fileID: 20}
  - {fileID: 21}
  - {fileID: 22}
  - {fileID: 23}
  - {fileID: 16}
  - {fileID: 24}
  m_Selected: 5
  m_LastSelected: 3
--- !u!114 &11
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12010, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Children:
  - {fileID: 12}
  - {fileID: 13}
  - {fileID: 14}
  m_Position:
    serializedVersion: 2
    x: 0
    y: 1011
    width: 1633
    height: 321
  m_MinSize: {x: 300, y: 50}
  m_MaxSize: {x: 24288, y: 8096}
  vertical: 0
  controlID: 159
  draggingID: 0
--- !u!114 &12
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: CubeLevelEditor
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 0
    y: 0
    width: 241
    height: 321
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_ActualView: {fileID: 26}
  m_Panes:
  - {fileID: 25}
  - {fileID: 26}
  m_Selected: 1
  m_LastSelected: 0
--- !u!114 &13
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ProjectBrowser
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 241
    y: 0
    width: 600
    height: 321
  m_MinSize: {x: 232, y: 276}
  m_MaxSize: {x: 10002, y: 10026}
  m_ActualView: {fileID: 27}
  m_Panes:
  - {fileID: 27}
  m_Selected: 0
  m_LastSelected: 0
--- !u!114 &14
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: ConsoleWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 841
    y: 0
    width: 792
    height: 321
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_ActualView: {fileID: 28}
  m_Panes:
  - {fileID: 28}
  - {fileID: 29}
  m_Selected: 0
  m_LastSelected: 1
--- !u!114 &15
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12006, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: InspectorWindow
  m_EditorClassIdentifier: 
  m_Children: []
  m_Position:
    serializedVersion: 2
    x: 1633
    y: 0
    width: 427
    height: 1332
  m_MinSize: {x: 275, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_ActualView: {fileID: 30}
  m_Panes:
  - {fileID: 30}
  - {fileID: 31}
  - {fileID: 32}
  - {fileID: 33}
  - {fileID: 34}
  m_Selected: 0
  m_LastSelected: 2
--- !u!114 &16
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12015, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Game
    m_Image: {fileID: -6423792434712278376, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Game\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1296
    y: 87
    width: 1383
    height: 985
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SerializedViewNames: []
  m_SerializedViewValues: []
  m_PlayModeViewName: GameView
  m_ShowGizmos: 0
  m_TargetDisplay: 0
  m_ClearColor: {r: 0, g: 0, b: 0, a: 0}
  m_TargetSize: {x: 1383, y: 964}
  m_TextureFilterMode: 0
  m_TextureHideFlags: 61
  m_RenderIMGUI: 1
  m_EnterPlayModeBehavior: 0
  m_UseMipMap: 0
  m_VSyncEnabled: 0
  m_Gizmos: 0
  m_Stats: 0
  m_SelectedSizes: 00000000000000000000000000000000000000000000000000000000000000000000000000000000
  m_ZoomArea:
    m_HRangeLocked: 0
    m_VRangeLocked: 0
    hZoomLockedByDefault: 0
    vZoomLockedByDefault: 0
    m_HBaseRangeMin: -691.5
    m_HBaseRangeMax: 691.5
    m_VBaseRangeMin: -482
    m_VBaseRangeMax: 482
    m_HAllowExceedBaseRangeMin: 1
    m_HAllowExceedBaseRangeMax: 1
    m_VAllowExceedBaseRangeMin: 1
    m_VAllowExceedBaseRangeMax: 1
    m_ScaleWithWindow: 0
    m_HSlider: 0
    m_VSlider: 0
    m_IgnoreScrollWheelUntilClicked: 0
    m_EnableMouseInput: 1
    m_EnableSliderZoomHorizontal: 0
    m_EnableSliderZoomVertical: 0
    m_UniformScale: 1
    m_UpDirection: 1
    m_DrawArea:
      serializedVersion: 2
      x: 0
      y: 21
      width: 1383
      height: 964
    m_Scale: {x: 1, y: 1}
    m_Translation: {x: 691.5, y: 482}
    m_MarginLeft: 0
    m_MarginRight: 0
    m_MarginTop: 0
    m_MarginBottom: 0
    m_LastShownAreaInsideMargins:
      serializedVersion: 2
      x: -691.5
      y: -482
      width: 1383
      height: 964
    m_MinimalGUI: 1
  m_defaultScale: 1
  m_LastWindowPixelSize: {x: 1383, y: 985}
  m_ClearInEditMode: 1
  m_NoCameraWarning: 1
  m_LowResolutionForAspectRatios: 01000000000000000000
  m_XRRenderMode: 0
  m_RenderTexture: {fileID: 0}
  m_showToolbar: 1
--- !u!114 &17
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12070, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 900, y: 216}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Profiler
    m_Image: {fileID: -1089619856830078684, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Profiler\u200B"
  m_Pos:
    serializedVersion: 2
    x: 0
    y: 79
    width: 363
    height: 664
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_Recording: 1
  m_ActiveNativePlatformSupportModuleName: 
  m_AllModules:
  - rid: 6377673351905411183
  - rid: 6377673351905411184
  - rid: 6377673351905411185
  - rid: 6377673351905411186
  - rid: 6377673351905411187
  - rid: 6377673351905411188
  - rid: 6377673351905411189
  - rid: 6377673351905411190
  - rid: 6377673351905411191
  - rid: 6377673351905411192
  - rid: 6377673351905411193
  - rid: 6377673351905411194
  - rid: 6377673351905411195
  - rid: 6377673351905411196
  m_CallstackRecordMode: 1
  m_ClearOnPlay: 0
  references:
    version: 2
    RefIds:
    - rid: 6377673351905411183
      type: {class: CPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.CPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 3
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 1
          m_TreeViewState:
            scrollPos: {x: 0, y: 16}
            m_SelectedIDs: 87010000
            m_LastClickedID: 0
            m_ExpandedIDs: c700000086010000870100009401000099010000ba010000bb010000e60100003802000050020000730200009402000023030000240300002503000013070000
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns:
            - width: 252
              sortedAscending: 1
              headerContent:
                m_Text: Overview
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Overview\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 200
              maxWidth: 1000000
              autoResize: 1
              allowToggleVisibility: 0
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Total
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Total\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Self\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Calls
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Calls\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: GC Alloc
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "GC Alloc\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Time ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Time ms\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 80
              sortedAscending: 0
              headerContent:
                m_Text: Self ms
                m_Image: {fileID: 0}
                m_Tooltip: 
                m_TextWithWhitespace: "Self ms\u200B"
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 50
              maxWidth: 1000000
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            - width: 25
              sortedAscending: 0
              headerContent:
                m_Text: 
                m_Image: {fileID: -5161429177145976760, guid: 0000000000000000d000000000000000, type: 0}
                m_Tooltip: Warnings
                m_TextWithWhitespace: 
              contextMenuText: 
              headerTextAlignment: 0
              sortingArrowAlignment: 2
              minWidth: 25
              maxWidth: 25
              autoResize: 0
              allowToggleVisibility: 1
              canSort: 1
              userData: 0
            m_VisibleColumns: 0000000001000000020000000300000004000000050000000600000007000000
            m_SortedColumns: 05000000
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: -1
            realSizes:
            - 0
            - 0
            relativeSizes:
            - 0.7
            - 0.3
            minSizes:
            - 450
            - 50
            maxSizes:
            - 0
            - 0
            lastTotalSize: 0
            splitSize: 6
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: -1
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 20732
          <threadIndex>k__BackingField: 0
          m_GroupName: 
    - rid: 6377673351905411184
      type: {class: GPUProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GPUProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewType: 0
        updateViewLive: 0
        m_CurrentFrameIndex: -1
        m_HierarchyOverruledThreadFromSelection: 0
        m_ProfilerViewFilteringOptions: 1
        m_FrameDataHierarchyView:
          m_Serialized: 0
          m_TreeViewState:
            scrollPos: {x: 0, y: 0}
            m_SelectedIDs: 
            m_LastClickedID: 0
            m_ExpandedIDs: 
            m_RenameOverlay:
              m_UserAcceptedRename: 0
              m_Name: 
              m_OriginalName: 
              m_EditFieldRect:
                serializedVersion: 2
                x: 0
                y: 0
                width: 0
                height: 0
              m_UserData: 0
              m_IsWaitingForDelay: 0
              m_IsRenaming: 0
              m_OriginalEventType: 11
              m_IsRenamingFilename: 0
              m_TrimLeadingAndTrailingWhitespace: 0
              m_ClientGUIView: {fileID: 0}
            m_SearchString: 
          m_MultiColumnHeaderState:
            m_Columns: []
            m_VisibleColumns: 
            m_SortedColumns: 
          m_ThreadIndexInThreadNames: 0
          m_DetailedViewType: 0
          m_DetailedViewSpliterState:
            ID: 0
            splitterInitialOffset: 0
            currentActiveSplitter: 0
            realSizes: []
            relativeSizes: []
            minSizes: []
            maxSizes: []
            lastTotalSize: 0
            splitSize: 0
            xOffset: 0
            m_Version: 1
            oldRealSizes: 
            oldMinSizes: 
            oldMaxSizes: 
            oldSplitSize: 0
          m_DetailedObjectsView:
            m_SelectedID: 0
            m_TreeViewState:
              scrollPos: {x: 0, y: 0}
              m_SelectedIDs: 
              m_LastClickedID: 0
              m_ExpandedIDs: 
              m_RenameOverlay:
                m_UserAcceptedRename: 0
                m_Name: 
                m_OriginalName: 
                m_EditFieldRect:
                  serializedVersion: 2
                  x: 0
                  y: 0
                  width: 0
                  height: 0
                m_UserData: 0
                m_IsWaitingForDelay: 0
                m_IsRenaming: 0
                m_OriginalEventType: 11
                m_IsRenamingFilename: 0
                m_TrimLeadingAndTrailingWhitespace: 0
                m_ClientGUIView: {fileID: 0}
              m_SearchString: 
            m_MultiColumnHeaderState:
              m_Columns: []
              m_VisibleColumns: 
              m_SortedColumns: 
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
          m_DetailedCallsView:
            m_SelectedID: -1
            m_VertSplit:
              ID: 0
              splitterInitialOffset: 0
              currentActiveSplitter: 0
              realSizes: []
              relativeSizes: []
              minSizes: []
              maxSizes: []
              lastTotalSize: 0
              splitSize: 0
              xOffset: 0
              m_Version: 1
              oldRealSizes: 
              oldMinSizes: 
              oldMaxSizes: 
              oldSplitSize: 0
            m_CalleesTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
            m_CallersTreeView:
              m_ViewState:
                scrollPos: {x: 0, y: 0}
                m_SelectedIDs: 
                m_LastClickedID: 0
                m_ExpandedIDs: 
                m_RenameOverlay:
                  m_UserAcceptedRename: 0
                  m_Name: 
                  m_OriginalName: 
                  m_EditFieldRect:
                    serializedVersion: 2
                    x: 0
                    y: 0
                    width: 0
                    height: 0
                  m_UserData: 0
                  m_IsWaitingForDelay: 0
                  m_IsRenaming: 0
                  m_OriginalEventType: 11
                  m_IsRenamingFilename: 0
                  m_TrimLeadingAndTrailingWhitespace: 0
                  m_ClientGUIView: {fileID: 0}
                m_SearchString: 
              m_ViewHeaderState:
                m_Columns:
                - width: 150
                  sortedAscending: 1
                  headerContent:
                    m_Text: Called From
                    m_Image: {fileID: 0}
                    m_Tooltip: 'Parents the selected function is called from


                      (Press
                      ''F'' for frame selection)'
                    m_TextWithWhitespace: "Called From\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 150
                  maxWidth: 1000000
                  autoResize: 1
                  allowToggleVisibility: 0
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Calls
                    m_Image: {fileID: 0}
                    m_Tooltip: Total number of calls in a selected frame
                    m_TextWithWhitespace: "Calls\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: GC Alloc
                    m_Image: {fileID: 0}
                    m_Tooltip: 
                    m_TextWithWhitespace: "GC Alloc\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time ms
                    m_Image: {fileID: 0}
                    m_Tooltip: Total time the selected function spends within a parent
                    m_TextWithWhitespace: "Time ms\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                - width: 60
                  sortedAscending: 0
                  headerContent:
                    m_Text: Time %
                    m_Image: {fileID: 0}
                    m_Tooltip: Shows how often the selected function was called from
                      the parent call
                    m_TextWithWhitespace: "Time %\u200B"
                  contextMenuText: 
                  headerTextAlignment: 0
                  sortingArrowAlignment: 2
                  minWidth: 60
                  maxWidth: 1000000
                  autoResize: 0
                  allowToggleVisibility: 1
                  canSort: 1
                  userData: 0
                m_VisibleColumns: 0000000001000000020000000300000004000000
                m_SortedColumns: 03000000
          m_FullThreadName: Main Thread
          m_ThreadName: Main Thread
          <threadId>k__BackingField: 0
          <threadIndex>k__BackingField: -1
          m_GroupName: 
    - rid: 6377673351905411185
      type: {class: RenderingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.RenderingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411186
      type: {class: MemoryProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.MemoryProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ViewSplit:
          ID: 0
          splitterInitialOffset: 0
          currentActiveSplitter: -1
          realSizes:
          - 0
          - 0
          relativeSizes:
          - 0.7
          - 0.3
          minSizes:
          - 450
          - 50
          maxSizes:
          - 0
          - 0
          lastTotalSize: 0
          splitSize: 6
          xOffset: 0
          m_Version: 1
          oldRealSizes: 
          oldMinSizes: 
          oldMaxSizes: 
          oldSplitSize: 0
    - rid: 6377673351905411187
      type: {class: AudioProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AudioProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_ShowInactiveDSPChains: 0
        m_HighlightAudibleDSPChains: 1
        m_DSPGraphZoomFactor: 1
        m_DSPGraphHorizontalLayout: 0
    - rid: 6377673351905411188
      type: {class: VideoProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VideoProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411189
      type: {class: PhysicsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.PhysicsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411190
      type: {class: Physics2DProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.Physics2DProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411191
      type: {class: UIProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411192
      type: {class: UIDetailsProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.UIDetailsProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411193
      type: {class: GlobalIlluminationProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.GlobalIlluminationProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411194
      type: {class: VirtualTexturingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.VirtualTexturingProfilerModule,
          UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
        m_VTProfilerView:
          rid: 6377673351905411197
    - rid: 6377673351905411195
      type: {class: FileIOProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.FileIOProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411196
      type: {class: AssetLoadingProfilerModule, ns: UnityEditorInternal.Profiling, asm: UnityEditor.CoreModule}
      data:
        m_Identifier: UnityEditorInternal.Profiling.AssetLoadingProfilerModule, UnityEditor.CoreModule,
          Version=0.0.0.0, Culture=neutral, PublicKeyToken=null
        m_PaneScroll: {x: 0, y: 0}
    - rid: 6377673351905411197
      type: {class: VirtualTexturingProfilerView, ns: UnityEditor, asm: UnityEditor.CoreModule}
      data:
        m_SortAscending: 0
        m_SortedColumn: -1
--- !u!114 &18
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12061, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 200, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Hierarchy
    m_Image: {fileID: 7966133145522015247, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Hierarchy\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1048
    y: 87
    width: 247
    height: 985
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SceneHierarchy:
    m_TreeViewState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 048cffff1c8cffff7896ffff16fbffff
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_TrimLeadingAndTrailingWhitespace: 0
        m_ClientGUIView: {fileID: 9}
      m_SearchString: 
    m_ExpandedScenes: []
    m_CurrenRootInstanceID: 0
    m_LockTracker:
      m_IsLocked: 0
    m_CurrentSortingName: TransformSorting
  m_WindowGUID: 4c969a2b90040154d917609493e03593
--- !u!114 &19
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 13855, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 310, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Preferences
    m_Image: {fileID: -5712115415447495865, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Preferences\u200B"
  m_Pos:
    serializedVersion: 2
    x: 903
    y: 131
    width: 1630
    height: 830
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_PosLeft: {x: 0, y: 0}
  m_PosRight: {x: 0, y: 0}
  m_Scope: 0
  m_SplitterPos: 168
  m_SearchText: 
  m_TreeViewState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: ce3102cc
    m_LastClickedID: -872271410
    m_ExpandedIDs: 2956c29689577ec10000000001fecd1127fb9b14
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
--- !u!114 &20
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 13854, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 310, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Project Settings
    m_Image: {fileID: -5712115415447495865, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project Settings\u200B"
  m_Pos:
    serializedVersion: 2
    x: 903
    y: 131
    width: 1630
    height: 830
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_PosLeft: {x: 0, y: 0}
  m_PosRight: {x: 0, y: 0}
  m_Scope: 1
  m_SplitterPos: 168
  m_SearchText: 
  m_TreeViewState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 53336be2
    m_LastClickedID: -496290989
    m_ExpandedIDs: 67e1b582d7dddea2a01a5fa667ea4eb100000000e594f01ac53aba5a
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
--- !u!114 &21
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 13999, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 972, y: 400}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: UI Builder
    m_Image: {fileID: 8683992553321208622, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "UI Builder\u200B"
  m_Pos:
    serializedVersion: 2
    x: 903
    y: 131
    width: 1630
    height: 830
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &22
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6629f1bb292b749a18b5fff7994c8b19, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 600, y: 350}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Unity Version Control
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Unity Version Control\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1407
    y: 87
    width: 1298
    height: 980
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  mForceToReOpen: 0
--- !u!114 &23
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12013, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Scene
    m_Image: {fileID: 2593428753322112591, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Scene\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1407
    y: 87
    width: 1298
    height: 980
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData:
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: Tool Settings
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-311.0,"y":146.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -311, y: 146}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: unity-grid-and-snap-toolbar
      index: 5
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-145.0,"y":-60.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -145, y: -60}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: unity-scene-view-toolbar
      index: 3
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-290.0009765625,"y":-173.9990234375},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -290.00098, y: -173.99902}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-toolbar__top
      displayed: 0
      id: unity-search-toolbar
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-241.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -241, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: unity-transform-toolbar
      index: 1
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--left
      displayed: 1
      id: unity-component-tools
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 197}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 2
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: Floating
      displayed: 0
      id: Orientation
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":true,"m_FloatingSnapOffset":{"x":-111.0,"y":163.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":1,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 1
      collapsed: 0
      snapOffset: {x: -111, y: 163}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 1
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Light Settings
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Camera
      index: 1
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Constraints
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Cloth Collisions
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Navmesh Display
      index: 4
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Agent Display
      index: 5
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Obstacle Display
      index: 6
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Occlusion Culling
      index: 3
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Physics Debugger
      index: 4
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Scene Visibility
      index: 5
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Particles
      index: 6
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap
      index: 11
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap Palette Helper
      index: 12
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 0
      id: Brush Attributes
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Terrain Tools
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Brush Masks
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Open Tile Palette
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tile Palette Clipboard
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":390.0,"y":300.0},"m_SizeOverridden":true}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 390, y: 300}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tilemap Focus
      index: 2
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Tile Palette Brush Pick
      index: 4
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":390.0,"y":300.0},"m_SizeOverridden":true}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 390, y: 300}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: APV Overlay
      index: 7
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Path
      index: 12
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Sprite Resolver
      index: 13
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 236, y: 152}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Cinemachine Tool Settings
      index: 14
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/TrailRenderer
      index: 8
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":48.0,"y":48.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: UnityEditor.SceneViewCameraOverlay
      index: 16
      contents: 
      floating: 0
      collapsed: 0
      snapOffset: {x: 48, y: 48}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 240, y: 135}
      sizeOverridden: 1
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: unity-scene-view-camera-mode-toolbar
      index: 4
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-196.0,"y":-30.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -196, y: -30}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--left
      displayed: 0
      id: Scene View/Lighting Visualization Colors
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-container--right
      displayed: 1
      id: Overlays/OverlayMenu
      index: 0
      contents: '{"m_Layout":1,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 1
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Sprite Swap
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: Floating
      displayed: 0
      id: SceneView/CamerasOverlay
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":true,"m_FloatingSnapOffset":{"x":-559.0,"y":-426.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":542.0,"y":387.0},"m_SizeOverridden":true}'
      floating: 1
      collapsed: 0
      snapOffset: {x: -559, y: -426}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 542, y: 387}
      sizeOverridden: 1
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/PBR Validation Settings
      index: 9
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: LDtkWorldDepthGUIDrawer
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-134.0009765625,"y":-77.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -134.00098, y: -77}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Event Tester
      index: 10
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Timeline Control
      index: 11
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect
      index: 12
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Visual Effect Model
      index: 13
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Folded":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: Floating
      displayed: 1
      id: AINavigationOverlay
      index: 0
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":true,"m_FloatingSnapOffset":{"x":-214.0009765625,"y":-242.0009765625},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 1
      collapsed: 0
      snapOffset: {x: -214.00098, y: -242.00098}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__top
      displayed: 0
      id: Brush Attributes (0)
      index: 3
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 0
      containerId: overlay-toolbar__left
      displayed: 0
      id: Brush Filters
      index: 1
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: brush-info-overlay
      index: 10
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: unity-spline-inspector
      index: 13
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Scene View/Animation Rigging
      index: 12
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":25.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 25}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: COZY Tools
      index: 13
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Hot Reload
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Chisel Snap Values
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Chisel Snap Settings
      index: 16
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Placement Options
      index: 17
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Chisel Global
      index: 18
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: Chisel Active Generator
      index: 19
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":-309.0,"y":-407.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":3,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: -309, y: -407}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 3
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: Surface Options
      index: 20
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":0.0,"y":0.0},"m_SnapOffsetDelta":{"x":24.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 0, y: 0}
      snapOffsetDelta: {x: 24, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 1
      id: UnityEditor.ProBuilder.MenuActionSettingsOverlay
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    - dockPosition: 1
      containerId: overlay-container--right
      displayed: 0
      id: ProBuilderTools
      index: 15
      contents: '{"m_Layout":4,"m_Collapsed":false,"m_Floating":false,"m_FloatingSnapOffset":{"x":24.0,"y":0.0},"m_SnapOffsetDelta":{"x":0.0,"y":0.0},"m_FloatingSnapCorner":0,"m_Size":{"x":0.0,"y":0.0},"m_SizeOverridden":false}'
      floating: 0
      collapsed: 0
      snapOffset: {x: 24, y: 0}
      snapOffsetDelta: {x: 0, y: 0}
      snapCorner: 0
      layout: 4
      size: {x: 0, y: 0}
      sizeOverridden: 0
    m_ContainerData:
    - containerId: overlay-toolbar__top
      scrollOffset: 0
    - containerId: overlay-toolbar__left
      scrollOffset: 0
    - containerId: overlay-container--left
      scrollOffset: 0
    - containerId: overlay-container--right
      scrollOffset: 0
    - containerId: overlay-toolbar__right
      scrollOffset: 0
    - containerId: overlay-toolbar__bottom
      scrollOffset: 0
    - containerId: Floating
      scrollOffset: 0
    m_OverlaysVisible: 1
  m_WindowGUID: 0ff93da3bfc969f469acc5aaf28523d1
  m_Gizmos: 1
  m_OverrideSceneCullingMask: 6917529027641081856
  m_SceneIsLit: 1
  m_SceneLighting: 1
  m_2DMode: 0
  m_isRotationLocked: 0
  m_PlayAudio: 0
  m_AudioPlay: 0
  m_DebugDrawModesUseInteractiveLightBakingData: 0
  m_Position:
    m_Target: {x: 36.195404, y: 200.36157, z: 1286.4379}
    speed: 2
    m_Value: {x: 36.195404, y: 200.36157, z: 1286.4379}
  m_RenderMode: 0
  m_CameraMode:
    drawMode: 0
    name: Shaded
    section: Shading Mode
  m_ValidateTrueMetals: 0
  m_DoValidateTrueMetals: 0
  m_SceneViewState:
    m_AlwaysRefresh: 0
    showFog: 1
    showSkybox: 1
    showFlares: 1
    showImageEffects: 1
    showParticleSystems: 1
    showVisualEffectGraphs: 1
    m_FxEnabled: 1
  m_Grid:
    xGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 0, y: 0}
    yGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 1, y: 1}
    zGrid:
      m_Fade:
        m_Target: 0
        speed: 2
        m_Value: 0
      m_Color: {r: 0.5, g: 0.5, b: 0.5, a: 0.4}
      m_Pivot: {x: 0, y: 0, z: 0}
      m_Size: {x: 0, y: 0}
    m_ShowGrid: 0
    m_GridAxis: 1
    m_gridOpacity: 0.5
  m_Rotation:
    m_Target: {x: 0.007358997, y: 0.49871725, z: 0.011022966, w: -0.86963296}
    speed: 2
    m_Value: {x: 0.0073400987, y: 0.4974365, z: 0.010994659, w: -0.8673997}
  m_Size:
    m_Target: 6.1202097
    speed: 2
    m_Value: 6.1202097
  m_Ortho:
    m_Target: 0
    speed: 2
    m_Value: 0
  m_CameraSettings:
    m_Speed: 2
    m_SpeedNormalized: 1
    m_SpeedMin: 0.01
    m_SpeedMax: 2
    m_EasingEnabled: 1
    m_EasingDuration: 0.4
    m_AccelerationEnabled: 1
    m_FieldOfViewHorizontalOrVertical: 60
    m_NearClip: 0.03
    m_FarClip: 10000
    m_DynamicClip: 1
    m_OcclusionCulling: 0
  m_LastSceneViewRotation: {x: 0.068913944, y: -0.6239462, z: 0.050874937, w: 0.77913475}
  m_LastSceneViewOrtho: 0
  m_Viewpoint:
    m_SceneView: {fileID: 23}
    m_CameraOverscanSettings:
      m_Opacity: 50
      m_Scale: 1
  m_ReplacementShader: {fileID: 0}
  m_ReplacementString: 
  m_SceneVisActive: 1
  m_LastLockedObject: {fileID: 0}
  m_LastDebugDrawMode:
    drawMode: 35
    name: Contributors / Receivers
    section: Lighting
  m_ViewIsLockedToObject: 0
--- !u!114 &24
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12373, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Audio Mixer
    m_Image: {fileID: 2344599766593239149, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Audio Mixer\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1344
    y: 128
    width: 1567
    height: 862
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_MixersTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 52664
    m_ExpandedIDs: 12eb343c
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 0
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 10}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_LayoutStripsOnTop:
    m_VerticalSplitter:
      ID: 0
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 65
      - 35
      relativeSizes:
      - 0.65
      - 0.35000002
      minSizes:
      - 85
      - 105
      maxSizes:
      - 0
      - 0
      lastTotalSize: 0
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
    m_HorizontalSplitter:
      ID: 0
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 60
      - 60
      - 60
      - 60
      relativeSizes:
      - 0.25
      - 0.25
      - 0.25
      - 0.25
      minSizes:
      - 85
      - 85
      - 85
      - 85
      maxSizes:
      - 0
      - 0
      - 0
      - 0
      lastTotalSize: 0
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
  m_LayoutStripsOnRight:
    m_VerticalSplitter:
      ID: 0
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 60
      - 60
      - 60
      - 60
      relativeSizes:
      - 0.25
      - 0.25
      - 0.25
      - 0.25
      minSizes:
      - 100
      - 85
      - 85
      - 85
      maxSizes:
      - 0
      - 0
      - 0
      - 0
      lastTotalSize: 0
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
    m_HorizontalSplitter:
      ID: 9843
      splitterInitialOffset: 0
      currentActiveSplitter: -1
      realSizes:
      - 470
      - 1097
      relativeSizes:
      - 0.3
      - 0.7
      minSizes:
      - 160
      - 160
      maxSizes:
      - 0
      - 0
      lastTotalSize: 1567
      splitSize: 6
      xOffset: 0
      m_Version: 1
      oldRealSizes: 
      oldMinSizes: 
      oldMaxSizes: 
      oldSplitSize: 0
  m_SectionOrder: 00000000030000000100000002000000
  m_LayoutMode: 1
  m_SortGroupsAlphabetically: 0
  m_ShowReferencedBuses: 1
  m_ShowBusConnections: 0
  m_ShowBusConnectionsOfSelection: 0
--- !u!114 &25
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -5467254957812901981, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project\u200B"
  m_Pos:
    serializedVersion: 2
    x: 858
    y: 1098
    width: 273
    height: 295
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_ReferencingInstanceIDs: 
    m_SceneHandles: 
    m_ShowAllHits: 0
    m_SkipHidden: 1
    m_SearchArea: 1
    m_Folders:
    - Assets/_Game/Audio/Player
    m_Globs: []
    m_ProductIds: 
    m_AnyWithAssetOrigin: 0
    m_OriginalText: 
    m_ImportLogFlags: 0
    m_FilterByTypeIntersection: 0
  m_ViewMode: 1
  m_StartGridSize: 16
  m_LastFolders:
  - Assets/_Game/Audio/Player
  m_LastFoldersGridSize: 16
  m_LastProjectPath: C:\Unity\BLAME\BLAME
  m_LockTracker:
    m_IsLocked: 1
  m_FolderTreeState:
    scrollPos: {x: 0, y: 72}
    m_SelectedIDs: 20620a00
    m_LastClickedID: 680480
    m_ExpandedIDs: 00000000f6fc0000f8fc0000fafc0000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 12}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 0ebd0000
    m_LastClickedID: 48398
    m_ExpandedIDs: 00000000f6fc0000f8fc0000fafc0000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: 
    m_LastClickedInstanceID: 0
    m_HadKeyboardFocusLastEvent: 0
    m_ExpandedInstanceIDs: c6230000f68f0000ea8f0000f08f000084e60000b6f100002ebd000002bd0000e2d000001ebe0000e4b80000cabe0000e6bc0000000000004cc60000f622010062ab0100f4f70000
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 0}
    m_GridSize: 16
  m_SkipHiddenPackages: 1
  m_DirectoriesAreaWidth: 105
--- !u!114 &26
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 052d089e46c6a4c45b1f88e2bf875801, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Cube Editor
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Cube Editor\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1048
    y: 1098
    width: 240
    height: 295
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &27
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12014, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 230, y: 250}
  m_MaxSize: {x: 10000, y: 10000}
  m_TitleContent:
    m_Text: Project
    m_Image: {fileID: -5467254957812901981, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Project\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1289
    y: 1098
    width: 598
    height: 295
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_SearchFilter:
    m_NameFilter: 
    m_ClassNames: []
    m_AssetLabels: []
    m_AssetBundleNames: []
    m_ReferencingInstanceIDs: 
    m_SceneHandles: 
    m_ShowAllHits: 0
    m_SkipHidden: 0
    m_SearchArea: 1
    m_Folders:
    - Assets/_Game/Scripts/Core
    m_Globs: []
    m_ProductIds: 
    m_AnyWithAssetOrigin: 0
    m_OriginalText: 
    m_ImportLogFlags: 0
    m_FilterByTypeIntersection: 0
  m_ViewMode: 1
  m_StartGridSize: 16
  m_LastFolders:
  - Assets/_Game/Scripts/Core
  m_LastFoldersGridSize: 16
  m_LastProjectPath: C:\Unity\BLAME\BLAME
  m_LockTracker:
    m_IsLocked: 0
  m_FolderTreeState:
    scrollPos: {x: 0, y: 300}
    m_SelectedIDs: 06fe0000
    m_LastClickedID: 65030
    m_ExpandedIDs: 00000000f6fc0000f8fc0000fafc000004fe000000ca9a3b
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 13}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_AssetTreeState:
    scrollPos: {x: 0, y: 0}
    m_SelectedIDs: 
    m_LastClickedID: 0
    m_ExpandedIDs: 00000000f6fc0000f8fc0000fafc0000
    m_RenameOverlay:
      m_UserAcceptedRename: 0
      m_Name: 
      m_OriginalName: 
      m_EditFieldRect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 0
        height: 0
      m_UserData: 0
      m_IsWaitingForDelay: 0
      m_IsRenaming: 0
      m_OriginalEventType: 11
      m_IsRenamingFilename: 1
      m_TrimLeadingAndTrailingWhitespace: 0
      m_ClientGUIView: {fileID: 0}
    m_SearchString: 
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
  m_ListAreaState:
    m_SelectedInstanceIDs: 
    m_LastClickedInstanceID: 0
    m_HadKeyboardFocusLastEvent: 0
    m_ExpandedInstanceIDs: fab8000062ab010070e60000627703004ad600001cf1fdffaec9fdff7ed80000503e0300f6e8000042ed0000e2d40400b80400003cfa00007ced15005cfa00007c9f03000000000028f30000
    m_CreateAssetUtility:
      m_EndAction: {fileID: 0}
      m_InstanceID: 0
      m_Path: 
      m_Icon: {fileID: 0}
      m_ResourceFile: 
    m_NewAssetIndexInList: -1
    m_ScrollPosition: {x: 0, y: 0}
    m_GridSize: 16
  m_SkipHiddenPackages: 0
  m_DirectoriesAreaWidth: 213
--- !u!114 &28
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12003, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 100, y: 100}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Console
    m_Image: {fileID: -4327648978806127646, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Console\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1889
    y: 1098
    width: 790
    height: 295
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &29
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7c66a740a74845a08dc18bab7f2ffa03, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 560, y: 200}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Recorder
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Recorder\u200B"
  m_Pos:
    serializedVersion: 2
    x: 1955
    y: 1042
    width: 907
    height: 300
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &30
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 1
  m_Script: {fileID: 12019, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 275, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Inspector
    m_Image: {fileID: -2667387946076563598, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Inspector\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2681
    y: 87
    width: 426
    height: 1306
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_ObjectsLockedBeforeSerialization: []
  m_InstanceIDsLockedBeforeSerialization: 
  m_PreviewResizer:
    m_CachedPref: -151
    m_ControlHash: -371814159
    m_PrefName: Preview_InspectorPreview
  m_LastInspectedObjectInstanceID: -1
  m_LastVerticalScrollValue: 0
  m_GlobalObjectId: 
  m_InspectorMode: 0
  m_LockTracker:
    m_IsLocked: 0
  m_PreviewWindow: {fileID: 0}
--- !u!114 &31
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc64e635488f60747bf5e9025c593285, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 250, y: 530}
  m_MaxSize: {x: 250, y: 531}
  m_TitleContent:
    m_Text: Bakery
    m_Image: {fileID: 2800000, guid: 9f3214aa72e84444bafd15f7a4c74fd5, type: 3}
    m_Tooltip: 
    m_TextWithWhitespace: "Bakery\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2793
    y: 79
    width: 646
    height: 1315
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  giSamples: 5
  priority: 3
  texelsPerUnit: 5
  userRenderMode: 0
  settingsMode: 0
  exeMode: 1
  deferredMode: 1
  unloadScenesInDeferredMode: 1
  fixSeams: 1
  denoise: 1
  denoise2x: 0
  encode: 1
  padding: 16
  dilate: 16
  selectedOnly: 0
  lightProbeRenderSize: 128
  lightProbeReadSize: 16
  lightProbeMaxCoeffs: 9
  renderSettingsStorage: {fileID: 0}
--- !u!114 &32
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12079, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 390, y: 390}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Lighting
    m_Image: {fileID: -1347227620855488341, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Lighting\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2931
    y: 136
    width: 483
    height: 1205
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &33
MonoBehaviour:
  m_ObjectHideFlags: 52
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 12090, guid: 0000000000000000e000000000000000, type: 0}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 300, y: 250}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Occlusion
    m_Image: {fileID: -3571815596324412722, guid: 0000000000000000d000000000000000, type: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Occlusion\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2677
    y: 87
    width: 762
    height: 1315
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
--- !u!114 &34
MonoBehaviour:
  m_ObjectHideFlags: 61
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 41147144ff556e246b736135eb26f185, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_MinSize: {x: 50, y: 50}
  m_MaxSize: {x: 4000, y: 4000}
  m_TitleContent:
    m_Text: Rendering Debugger
    m_Image: {fileID: 0}
    m_Tooltip: 
    m_TextWithWhitespace: "Rendering Debugger\u200B"
  m_Pos:
    serializedVersion: 2
    x: 2549
    y: 154
    width: 483
    height: 1205
  m_SerializedDataModeController:
    m_DataMode: 0
    m_PreferredDataMode: 0
    m_SupportedDataModes: 
    isAutomatic: 1
  m_ViewDataDictionary: {fileID: 0}
  m_OverlayCanvas:
    m_LastAppliedPresetName: Default
    m_SaveData: []
    m_ContainerData: []
    m_OverlaysVisible: 1
  m_WidgetStates:
    m_Keys: []
    m_Values: []
  m_Settings: {fileID: 0}
