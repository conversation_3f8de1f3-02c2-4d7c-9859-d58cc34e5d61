{ "pid": 154716, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 154716, "tid": 1, "ts": 1755645927904181, "dur": 5620, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 154716, "tid": 1, "ts": 1755645927909805, "dur": 108535, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 154716, "tid": 1, "ts": 1755645928018349, "dur": 2763, "ph": "X", "name": "Write<PERSON><PERSON>", "args": {} },
{ "pid": 154716, "tid": 167, "ts": 1755645929600249, "dur": 1343, "ph": "X", "name": "", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645927901995, "dur": 20730, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645927922728, "dur": 1663711, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645927923982, "dur": 2225, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645927926212, "dur": 1507, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645927927722, "dur": 50, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645927927776, "dur": 752, "ph": "X", "name": "ProcessMessages 483", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645927928531, "dur": 4069, "ph": "X", "name": "ReadAsync 483", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645927932603, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645927932605, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645927932638, "dur": 651, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645927933292, "dur": 1634130, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645929567430, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645929567434, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645929567466, "dur": 1525, "ph": "X", "name": "ProcessMessages 15973", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645929568994, "dur": 6223, "ph": "X", "name": "ReadAsync 15973", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645929575225, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645929575229, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645929575262, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645929575354, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645929575356, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645929575375, "dur": 357, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 154716, "tid": 12884901888, "ts": 1755645929575735, "dur": 9958, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 154716, "tid": 167, "ts": 1755645929601596, "dur": 35, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 154716, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": { "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync" } },
{ "pid": 154716, "tid": 8589934592, "ts": 1755645927899793, "dur": 121349, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {} },
{ "pid": 154716, "tid": 8589934592, "ts": 1755645928021144, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {} },
{ "pid": 154716, "tid": 8589934592, "ts": 1755645928021148, "dur": 863, "ph": "X", "name": "WriteDagReadyMessage", "args": {} },
{ "pid": 154716, "tid": 167, "ts": 1755645929601633, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {} },
{ "pid": 154716, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 154716, "tid": 4294967296, "ts": 1755645927882592, "dur": 1704926, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 154716, "tid": 4294967296, "ts": 1755645927886495, "dur": 9189, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 154716, "tid": 4294967296, "ts": 1755645929587580, "dur": 5825, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 154716, "tid": 4294967296, "ts": 1755645929591257, "dur": 32, "ph": "X", "name": "await ScriptUpdaters", "args": {} },
{ "pid": 154716, "tid": 4294967296, "ts": 1755645929593532, "dur": 18, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {} },
{ "pid": 154716, "tid": 167, "ts": 1755645929601641, "dur": 8, "ph": "X", "name": "BuildAsync", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1755645927921149, "dur":50, "ph":"X", "name": "IPC_Client_InitializeAndConnectToParent",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755645927921245, "dur":4196, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755645927925455, "dur":158, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755645927925645, "dur":588, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755645927926304, "dur":54, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"ScriptAssemblies" }}
,{ "pid":12345, "tid":0, "ts":1755645927926272, "dur":111, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755645927926384, "dur":1649433, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755645929575993, "dur":4406, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1755645927926302, "dur":97, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1755645927927487, "dur":753, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\ScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":1, "ts":1755645927926412, "dur":6039, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"BuildPlayerDataGenerator Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":1, "ts":1755645927934218, "dur":1635196, "ph":"X", "name": "BuildPlayerDataGenerator",  "args": { "detail":"Library/BuildPlayerData/Editor/TypeDb-All.json" }}
,{ "pid":12345, "tid":2, "ts":1755645927926353, "dur":57, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755645927926431, "dur":106018, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1755645928032449, "dur":1543316, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1755645927926751, "dur":1649016, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755645927926418, "dur":96235, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755645928032092, "dur":348, "ph":"X", "name": "CheckGlobSignature",  "args": { "detail":"C:/Unity/Editors/6000.2.0f1/Editor/Data/Tools/BuildPipeline" }}
,{ "pid":12345, "tid":4, "ts":1755645928022654, "dur":9791, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1755645928032445, "dur":1543110, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1755645927926447, "dur":1649093, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1755645927926493, "dur":1649212, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1755645927926530, "dur":1649161, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1755645927926578, "dur":1649235, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1755645927926605, "dur":1649195, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1755645927926662, "dur":1648872, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1755645927926693, "dur":1648867, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1755645927926734, "dur":1648955, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1755645929585402, "dur":413, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 154716, "tid": 167, "ts": 1755645929602118, "dur": 1969, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 154716, "tid": 167, "ts": 1755645929604249, "dur": 2381, "ph": "X", "name": "backend1.traceevents", "args": {} },
{ "pid": 154716, "tid": 167, "ts": 1755645929599385, "dur": 8224, "ph": "X", "name": "Write chrome-trace events", "args": {} },
