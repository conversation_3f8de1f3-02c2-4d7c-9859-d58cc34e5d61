using UnityEngine;
using KinematicCharacterController;
using System.Collections;
using System.Collections.Generic;

public class InvItemDropping : MonoBehaviour
{
    [SerializeField] private GameObject itemDropPrefab;
    [SerializeField] private EquipmentManager equipmentManager;
    [SerializeField] private PersistenceManager progressionManager;
    
    [SerializeField] private bool useGlobalModelOverrides = false;
    [SerializeField] private ItemModelDictionary modelOverrides;
    
    [System.Serializable]
    public class ItemModelDictionary
    {
        [System.Serializable]
        public class ItemModelPair
        {
            public string itemName;
            public GameObject modelPrefab;
        }
        
        public ItemModelPair[] pairs;
        
        public GameObject GetModelForItem(string itemName)
        {
            if (pairs == null) return null;
            
            foreach (var pair in pairs)
            {
                if (pair.itemName == itemName)
                    return pair.modelPrefab;
            }
            return null;
        }
    }
    
    public float throwForce = 8f;
    public float throwUpwardForce = 2f;
    public float dropOffset = 1.5f;

    private Camera playerCamera;

    [Header("Platform Drop Settings")]
    [Tooltip("Extra upward velocity when dropping from high-speed platforms")]
    [SerializeField] private float highSpeedDropUpBoost = 5f;
    
    [Tooltip("How much platform velocity to inherit (0-1)")]
    [SerializeField] private float platformVelocityInheritance = 0.8f;
    
    [Tooltip("Minimum time before item can be re-captured by platform zone")]
    [SerializeField] private float dropImmunityTime = 1.5f;
    
    // Track recently dropped items to prevent immediate re-capture
    private static HashSet<GameObject> recentlyDroppedItems = new HashSet<GameObject>();
    
    public GameObject GetItemDropPrefab() => itemDropPrefab;

    private void Start()
    {
        if (Camera.main != null)
        {
            playerCamera = Camera.main;
        }
        else
        {
            GameObject cameraObj = GameObject.FindGameObjectWithTag("MainCamera");
            if (cameraObj != null)
                playerCamera = cameraObj.GetComponent<Camera>();
        }
        
        if (playerCamera == null)
            Debug.LogWarning("InvItemDropping: Could not find main camera. Using fallback for drop positions.");
        
        if (equipmentManager == null)
        {
            equipmentManager = FindFirstObjectByType<EquipmentManager>();
            if (equipmentManager == null)
                Debug.LogError("InvItemDropping: EquipmentManager reference is missing!");
        }
        
        if (progressionManager == null)
        {
            progressionManager = PersistenceManager.Instance;
            if (progressionManager == null)
                Debug.LogError("InvItemDropping: PersistenceManager reference is missing!");
        }
    }
    
    private GameObject SpawnItemPrefab(Item itemToDrop, Vector3 position, Quaternion rotation)
    {
        GameObject prefabToSpawn;
            
        if (useGlobalModelOverrides && modelOverrides != null)
        {
            GameObject overrideModel = modelOverrides.GetModelForItem(itemToDrop.itemName);
            if (overrideModel != null)
                prefabToSpawn = overrideModel;
            else if (itemToDrop.WorldModelPrefab != null)
                prefabToSpawn = itemToDrop.WorldModelPrefab;
            else
                prefabToSpawn = itemDropPrefab;
        }
        else if (itemToDrop.WorldModelPrefab != null)
        {
            prefabToSpawn = itemToDrop.WorldModelPrefab;
        }
        else
        {
            prefabToSpawn = itemDropPrefab;
        }
        
        GameObject droppedItem = Instantiate(prefabToSpawn, position, rotation);
        EnsureRequiredComponents(droppedItem, itemToDrop);
        
        InvItemModelSwapper modelSwapper = droppedItem.GetComponent<InvItemModelSwapper>();
        if (modelSwapper != null)
            Destroy(modelSwapper);
        
        return droppedItem;
    }

    private PlatformDropContext GetPlatformContext()
    {
        var context = new PlatformDropContext();
        
        // Check if player is in a passenger zone
        Collider[] overlaps = Physics.OverlapSphere(transform.position, 0.5f, ~0, QueryTriggerInteraction.Collide);
        
        foreach (var col in overlaps)
        {
            if (col == null) continue;
            
            var zone = col.GetComponentInParent<KinematicPlatformPassengerZone>();
            if (zone == null) continue;
            
            context.isInZone = true;
            context.zone = zone;
            
            // Get platform reference
            var platform = zone.GetComponentInParent<KinematicPlatform>();
            if (platform != null)
            {
                context.platform = platform;
                var mover = platform.GetComponent<PhysicsMover>();
                if (mover != null)
                {
                    context.platformVelocity = mover.Velocity;
                    context.platformAngularVelocity = mover.AngularVelocity;
                }
            }
            
            break; // Found a zone, that's enough
        }
        
        return context;
    }
    
    private class PlatformDropContext
    {
        public bool isInZone;
        public KinematicPlatformPassengerZone zone;
        public KinematicPlatform platform;
        public Vector3 platformVelocity;
        public Vector3 platformAngularVelocity;
    }
    
    private void EnsureRequiredComponents(GameObject droppedItem, Item item)
    {
        Rigidbody rb = droppedItem.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = droppedItem.AddComponent<Rigidbody>();
            rb.mass = 1f;
            rb.linearDamping = 0.5f;
            rb.angularDamping = 0.2f;
            rb.collisionDetectionMode = CollisionDetectionMode.Continuous;
            rb.isKinematic = false;
            rb.useGravity = true;
        }
        
        Collider collider = droppedItem.GetComponent<Collider>();
        if (collider == null)
        {
            Collider[] childColliders = droppedItem.GetComponentsInChildren<Collider>();
            if (childColliders.Length == 0)
            {
                BoxCollider boxCollider = droppedItem.AddComponent<BoxCollider>();
                boxCollider.size = new Vector3(0.5f, 0.5f, 0.5f);
                boxCollider.center = Vector3.zero;
            }
        }
        
        InvItemPickup itemPickup = droppedItem.GetComponent<InvItemPickup>();
        if (itemPickup == null)
        {
            itemPickup = droppedItem.AddComponent<InvItemPickup>();
        }
    }
    
    public void ThrowItem(Item itemToBeThrown, int quantity = 1, bool isEquipment = false)
    {
        var context = GetPlatformContext();
        
        GameObject playerObject = gameObject;
        Vector3 dropPosition;
        Vector3 dropDirection;

        if (playerCamera != null)
        {
            dropPosition = playerCamera.transform.position + playerCamera.transform.forward * dropOffset;
            dropDirection = playerCamera.transform.forward;
        }
        else
        {
            dropPosition = transform.position + transform.forward * dropOffset;
            dropPosition.y += 1.5f;
            dropDirection = transform.forward;
        }

        GameObject droppedItemInstance = SpawnItemPrefab(itemToBeThrown, dropPosition, Quaternion.identity);
        InvItemPickup invItemPickup = droppedItemInstance.GetComponent<InvItemPickup>();

        if (invItemPickup != null)
        {
            invItemPickup.SetItem(itemToBeThrown, quantity);

            if (isEquipment && itemToBeThrown is Bag)
            {
                var droppedStorage = droppedItemInstance.GetComponent<InvDroppedStorageEquipment>();
                if (droppedStorage == null)
                {
                    droppedStorage = droppedItemInstance.AddComponent<InvDroppedStorageEquipment>();
                }
            }

            invItemPickup.MarkAsPlayerDropped();

            Rigidbody rb = droppedItemInstance.GetComponent<Rigidbody>();
            if (rb != null)
            {
                // Calculate throw velocity
                Vector3 throwVelocity = dropDirection * throwForce + Vector3.up * throwUpwardForce;
                
                // If on a platform, add platform velocity
                if (context.isInZone)
                {
                    // Inherit most of the platform's velocity
                    throwVelocity += context.platformVelocity * platformVelocityInheritance;
                    
                    // Add extra upward velocity for high-speed platforms to help clear the platform
                    float platformSpeed = context.platformVelocity.magnitude;
                    if (platformSpeed > 20f)
                    {
                        float speedFactor = Mathf.Min(platformSpeed / 100f, 1f);
                        throwVelocity += Vector3.up * highSpeedDropUpBoost * speedFactor;
                    }
                    
                    // Apply angular velocity contribution if platform is rotating
                    if (context.platformAngularVelocity.magnitude > 0.01f && context.platform != null)
                    {
                        Vector3 relativePos = dropPosition - context.platform.transform.position;
                        Vector3 tangentialVelocity = Vector3.Cross(context.platformAngularVelocity, relativePos);
                        throwVelocity += tangentialVelocity * platformVelocityInheritance;
                    }
                    
                    // Mark item as immune to platform zone for a brief period
                    MarkItemAsDropImmune(droppedItemInstance, context.zone);
                }
                
                rb.isKinematic = false;
                rb.linearVelocity = throwVelocity;
                rb.AddTorque(new Vector3(
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f)
                ).normalized * 2f, ForceMode.Impulse);
            }
        }
        else
        {
            Debug.LogError("InvItemPickup component not found on the spawned prefab.");
            Destroy(droppedItemInstance);
        }
    }

    public GameObject DropItem(Item itemToDrop, int quantity, bool isEquipment = false)
    {
        if (itemToDrop == null)
        {
            Debug.LogError("Attempted to drop null item");
            return null;
        }
        
        var context = GetPlatformContext();
        
        bool isStorageItem = isEquipment && itemToDrop is Bag;
        EquipmentBase itemToBeDropped = isEquipment ? (EquipmentBase)itemToDrop : null;

        Vector3 dropPosition;
        Vector3 dropDirection;
        if (playerCamera != null)
        {
            dropPosition = playerCamera.transform.position + playerCamera.transform.forward * dropOffset;
            dropDirection = playerCamera.transform.forward;
        }
        else
        {
            dropPosition = transform.position + transform.forward * dropOffset;
            dropPosition.y += 1.5f;
            dropDirection = transform.forward;
        }
        
        string serializedContent = "";
        if (isStorageItem)
        {
            EquipmentSlotType slotType = itemToBeDropped.Slot;
            var slot = equipmentManager.GetEquipmentSlot(slotType);
            if (slot?.equippedItem != null && slot.equippedItem == itemToBeDropped)
            {
                serializedContent = equipmentManager.SerializeContainerContent(slotType);
            }
            
            if (slot?.equippedItem == itemToBeDropped)
            {
                equipmentManager.UnequipItem(slotType, true);
            }
        }
        
        GameObject droppedObject = SpawnItemPrefab(itemToDrop, dropPosition, Quaternion.identity);
        InvItemPickup droppedItemComponent = droppedObject.GetComponent<InvItemPickup>();
        
        if (droppedItemComponent != null)
        {
            droppedItemComponent.SetItem(itemToDrop, quantity);
            
            if (isStorageItem && !string.IsNullOrEmpty(serializedContent))
            {
                var storageComponent = droppedObject.GetComponent<InvDroppedStorageEquipment>();
                if (storageComponent == null)
                {
                    storageComponent = droppedObject.AddComponent<InvDroppedStorageEquipment>();
                }
                storageComponent.StorageContent = serializedContent;
            }
            
            Rigidbody rb = droppedObject.GetComponent<Rigidbody>();
            if (rb != null)
            {
                Vector3 throwVelocity = dropDirection * throwForce + Vector3.up * throwUpwardForce;
                
                if (context.isInZone)
                {
                    throwVelocity += context.platformVelocity * platformVelocityInheritance;
                    
                    float platformSpeed = context.platformVelocity.magnitude;
                    if (platformSpeed > 20f)
                    {
                        float speedFactor = Mathf.Min(platformSpeed / 100f, 1f);
                        throwVelocity += Vector3.up * highSpeedDropUpBoost * speedFactor;
                    }
                    
                    if (context.platformAngularVelocity.magnitude > 0.01f && context.platform != null)
                    {
                        Vector3 relativePos = dropPosition - context.platform.transform.position;
                        Vector3 tangentialVelocity = Vector3.Cross(context.platformAngularVelocity, relativePos);
                        throwVelocity += tangentialVelocity * platformVelocityInheritance;
                    }
                    
                    MarkItemAsDropImmune(droppedObject, context.zone);
                }
                
                rb.isKinematic = false;
                rb.linearVelocity = throwVelocity;
                rb.AddTorque(new Vector3(
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f)
                ).normalized * 2f, ForceMode.Impulse);
            }
            
            droppedItemComponent.MarkAsPlayerDropped();
            
            return droppedObject;
        }
        else
        {
            Debug.LogError("InvItemPickup component not found on spawned item prefab.");
            Destroy(droppedObject);
            return null;
        }
    }

    public void CreateDroppedItemInstance(
        GameObject playerObject,
        string itemName,
        Vector2 screenPosition,
        bool isEquipment,
        int equipmentSlotTypeInt,
        string serializedContainerContent,
        int quantity = 1)
    {
        if (string.IsNullOrEmpty(itemName))
        {
            Debug.LogError("Item name is null or empty. Cannot drop item.");
            return;
        }

        Item itemToBeDropped = ItemDatabase.GetItemByName(itemName);
        if (itemToBeDropped == null)
        {
            Debug.LogError($"Item with name '{itemName}' not found in database. Cannot drop item.");
            return;
        }
        
        var context = GetPlatformContext();

        Vector3 dropPosition;
        Vector3 dropDirection;
        if (playerCamera != null)
        {
            dropPosition = playerCamera.transform.position + playerCamera.transform.forward * dropOffset;
            dropDirection = playerCamera.transform.forward;
        }
        else
        {
            dropPosition = playerObject.transform.position + playerObject.transform.forward * dropOffset;
            dropPosition.y += 1.5f;
            dropDirection = playerObject.transform.forward;
        }

        GameObject droppedItemInstance = SpawnItemPrefab(itemToBeDropped, dropPosition, Quaternion.identity);
        InvItemPickup invItemPickup = droppedItemInstance.GetComponent<InvItemPickup>();
        
        if (invItemPickup != null)
        {
            invItemPickup.SetItem(itemToBeDropped, quantity);
            invItemPickup.SetTimeSinceThrown(Time.time);
            
            if (isEquipment && itemToBeDropped is Bag)
            {
                var droppedStorage = droppedItemInstance.GetComponent<InvDroppedStorageEquipment>();
                if (droppedStorage == null)
                {
                    droppedStorage = droppedItemInstance.AddComponent<InvDroppedStorageEquipment>();
                }
                
                droppedStorage.SetContainerSnapshot(serializedContainerContent);
            }
            
            invItemPickup.MarkAsPlayerDropped();
            
            Rigidbody rb = droppedItemInstance.GetComponent<Rigidbody>();
            if (rb != null)
            {
                Vector3 throwVelocity = dropDirection * throwForce + Vector3.up * throwUpwardForce;
                
                if (context.isInZone)
                {
                    throwVelocity += context.platformVelocity * platformVelocityInheritance;
                    
                    float platformSpeed = context.platformVelocity.magnitude;
                    if (platformSpeed > 20f)
                    {
                        float speedFactor = Mathf.Min(platformSpeed / 100f, 1f);
                        throwVelocity += Vector3.up * highSpeedDropUpBoost * speedFactor;
                    }
                    
                    if (context.platformAngularVelocity.magnitude > 0.01f && context.platform != null)
                    {
                        Vector3 relativePos = dropPosition - context.platform.transform.position;
                        Vector3 tangentialVelocity = Vector3.Cross(context.platformAngularVelocity, relativePos);
                        throwVelocity += tangentialVelocity * platformVelocityInheritance;
                    }
                    
                    MarkItemAsDropImmune(droppedItemInstance, context.zone);
                }
                
                rb.isKinematic = false;
                rb.linearVelocity = throwVelocity;
                rb.AddTorque(new Vector3(
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f),
                    Random.Range(-1f, 1f)
                ).normalized * 2f, ForceMode.Impulse);
            }
        }
        else
        {
            Debug.LogError("InvItemPickup component not found on the spawned prefab.");
            Destroy(droppedItemInstance);
        }
    }
    
    private void MarkItemAsDropImmune(GameObject item, KinematicPlatformPassengerZone zone)
    {
        // Add to recently dropped items set
        recentlyDroppedItems.Add(item);
        
        // Start coroutine to remove immunity after delay
        StartCoroutine(RemoveDropImmunity(item, dropImmunityTime));
        
        // Add temporary component to mark immunity
        var immunity = item.AddComponent<PlatformDropImmunity>();
        immunity.immuneUntilTime = Time.time + dropImmunityTime;
        immunity.immuneFromZone = zone;
    }
    
    private IEnumerator RemoveDropImmunity(GameObject item, float delay)
    {
        yield return new WaitForSeconds(delay);
        
        if (item != null)
        {
            recentlyDroppedItems.Remove(item);
            
            var immunity = item.GetComponent<PlatformDropImmunity>();
            if (immunity != null)
                Destroy(immunity);
        }
    }
    
    public static bool IsItemDropImmune(GameObject item)
    {
        if (item == null) return false;
        
        var immunity = item.GetComponent<PlatformDropImmunity>();
        if (immunity != null)
        {
            if (Time.time < immunity.immuneUntilTime)
                return true;
            else
            {
                // Cleanup expired immunity
                recentlyDroppedItems.Remove(item);
                Destroy(immunity);
            }
        }
        
        return recentlyDroppedItems.Contains(item);
    }
}

// Helper component to track drop immunity
public class PlatformDropImmunity : MonoBehaviour
{
    public float immuneUntilTime;
    public KinematicPlatformPassengerZone immuneFromZone;
}