{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1755645967099287, "dur": 509662, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755645967100793, "dur": 55715, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755645967164227, "dur": 409497, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755645967165023, "dur": 363483, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755645967165643, "dur": 68341, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755645967234522, "dur": 959, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755645967270701, "dur": 230784, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755645967581428, "dur": 1237, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755645967582667, "dur": 26273, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755645967583830, "dur": 20919, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755645967616417, "dur": 1396, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1755645967615952, "dur": 2124, "ph": "X", "name": "Write chrome-trace events", "args": {} },
