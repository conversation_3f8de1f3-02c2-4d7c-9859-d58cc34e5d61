using UnityEngine;
using UnityEngine.UI;
using System.Collections;

public class CrosshairManager : MonoBehaviour
{
    #region Inspector Variables

    [Header("Crosshair Settings")]
    [SerializeField] private Color crosshairColor = Color.white;
    [SerializeField] private float crosshairSize = 4f;
    [SerializeField] private bool screenScaleCompensation = true;
    [SerializeField] private Sprite customCrosshairSprite;
    [SerializeField] private bool useCustomCrosshair = false;
    [SerializeField] private bool alwaysShowCrosshair = false;
    
    [Header("Overlay Settings")]
    [SerializeField] private Sprite overlayImageSprite;
    [SerializeField] private Vector2 overlaySize = new Vector2(100f, 100f);
    [SerializeField] private Color overlayColor = Color.white;
    [SerializeField] private bool showOverlayDuringGrab = true;
    [SerializeField] private bool showOverlayDuringHold = true;
    
    [Header("Interaction Detection")]
    [Tooltip("Distance at which to show crosshair for interactables")]
    [SerializeField] private float interactionDetectionDistance = 3f;
    [Tooltip("How quickly the crosshair fades in/out")]
    [SerializeField] private float crosshairFadeSpeed = 5f;
    [Tooltip("Maximum opacity when looking at interactables (0-1)")]
    [SerializeField] private float interactableMaxOpacity = 1f;
    
    [Header("Item Proximity Detection")]
    [Tooltip("Radius to check for nearby items")]
    [SerializeField] private float itemProximityRadius = 5f;
    [Tooltip("Opacity when items are nearby but not looked at (0-1)")]
    [SerializeField] private float itemProximityOpacity = 0.3f;
    [Tooltip("Enable proximity-based crosshair for items")]
    [SerializeField] private bool enableItemProximity = true;
    [Tooltip("How often to check for nearby items (seconds)")]
    [SerializeField] private float proximityCheckInterval = 0.2f;

    [Header("Optimization Settings")]
    [Tooltip("How often to update raycasts (in seconds)")]
    [SerializeField] private float raycastUpdateInterval = 0.05f;
    [Tooltip("Minimum camera movement before forcing a raycast update")]
    [SerializeField] private float cameraMovementThreshold = 0.01f;
    [Tooltip("Minimum camera rotation before forcing a raycast update (degrees)")]
    [SerializeField] private float cameraRotationThreshold = 1f;
    
    [Header("References")]
    [SerializeField] private Camera playerCamera;
    [SerializeField] private LayerMask interactionLayers = -1;

    #endregion

    #region Private Variables

    private Canvas uiCanvas;
    private Image crosshairDot;
    private Image overlayImage;
    private float baseScreenHeight = 1080f;
    private bool needsTextureUpdate = false;

    // State tracking
    private bool isLookingAtInteractable = false;
    private bool isLookingAtItem = false;
    private bool itemsNearby = false;
    private bool isGrabbingOrHolding = false;
    private bool isCurrentlyHolding = false; // Separate tracking for hold state
    private bool forceCrosshairVisible = false;
    private bool isLookingAtScreen = false;
    
    // Opacity tracking
    private float currentCrosshairOpacity = 0f;
    private float targetCrosshairOpacity = 0f;
    
    // Performance optimization
    private float lastRaycastTime = 0f;
    private float lastProximityCheckTime = 0f;
    private Vector3 lastCameraPosition;
    private Quaternion lastCameraRotation;
    
    // Cached data for performance
    private RaycastHit[] raycastHits = new RaycastHit[10];
    private Collider[] nearbyColliders = new Collider[20];
    private Color baseCrosshairColor;
    
    // References to other systems
    private GrabInteraction grabInteraction;
    
    // Debug tracking
    private bool lastLoggedGrabState = false;

    #endregion

    #region Unity Messages

    private void Awake()
    {
        if (playerCamera == null)
            playerCamera = Camera.main;

        SetupUICanvas();
        SetupCrosshair();
        SetupOverlayImage();
        
        baseCrosshairColor = crosshairColor;
        
        if (!alwaysShowCrosshair && crosshairDot != null)
        {
            crosshairDot.gameObject.SetActive(false);
        }
        
        // Initialize camera tracking
        if (playerCamera != null)
        {
            lastCameraPosition = playerCamera.transform.position;
            lastCameraRotation = playerCamera.transform.rotation;
        }
        
        // Find references to other systems
        grabInteraction = FindObjectOfType<GrabInteraction>();
        
        if (grabInteraction == null)
        {
            Debug.LogWarning("[CrosshairManager] GrabInteraction not found! Overlay won't show during grab.");
        }
        else
        {
            Debug.Log("[CrosshairManager] GrabInteraction found successfully");
        }
    }

    private void OnValidate()
    {
        if (crosshairDot != null || overlayImage != null)
        {
            needsTextureUpdate = true;
            UpdateCrosshairAppearance();
            UpdateOverlayAppearance();
            UpdateCrosshairVisibility();
        }
    }

    private void Update()
    {
        // Check grab/hold state
        UpdateGrabHoldState();
        
        // Debug logging for grab state (only log changes to avoid spam)
        if (grabInteraction != null)
        {
            bool currentGrabState = grabInteraction.IsGrabbing;
            if (currentGrabState != lastLoggedGrabState)
            {
                Debug.Log($"[CrosshairManager] Grab state changed: {currentGrabState}");
                lastLoggedGrabState = currentGrabState;
            }
        }
        
        // Check for nearby items periodically
        if (enableItemProximity && Time.time - lastProximityCheckTime >= proximityCheckInterval)
        {
            CheckNearbyItems();
        }
        
        // Update raycasts based on camera movement
        if (ShouldUpdateRaycast())
        {
            PerformInteractionRaycast();
        }
        
        // Always update crosshair fade
        UpdateCrosshairFade();
    }

    private void LateUpdate()
    {
        if (needsTextureUpdate)
        {
            UpdateCrosshairTextures();
            needsTextureUpdate = false;
        }
    }

    #endregion

    #region Setup Methods

    private void SetupUICanvas()
    {
        GameObject canvasObj = new GameObject("UI Canvas");
        uiCanvas = canvasObj.AddComponent<Canvas>();
        uiCanvas.renderMode = RenderMode.ScreenSpaceOverlay;

        CanvasScaler scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
        scaler.matchWidthOrHeight = 1;

        canvasObj.AddComponent<GraphicRaycaster>();
        canvasObj.transform.SetParent(transform);
    }

    private void SetupCrosshair()
    {
        GameObject dotObj = new GameObject("CrosshairDot");
        dotObj.transform.SetParent(uiCanvas.transform);
        crosshairDot = dotObj.AddComponent<Image>();

        UpdateCrosshairTextures();
        UpdateCrosshairAppearance();
        
        crosshairDot.gameObject.SetActive(alwaysShowCrosshair);
    }
    
    private void SetupOverlayImage()
    {
        GameObject overlayObj = new GameObject("OverlayImage");
        overlayObj.transform.SetParent(uiCanvas.transform);
        overlayImage = overlayObj.AddComponent<Image>();

        // If no sprite is assigned, create a default one
        if (overlayImageSprite == null)
        {
            Debug.LogWarning("[CrosshairManager] No overlay sprite assigned, creating default circle overlay");
            CreateDefaultOverlaySprite();
        }

        UpdateOverlayAppearance();
        overlayImage.gameObject.SetActive(false);
    }

    #endregion

    #region Texture Generation

    private void UpdateCrosshairTextures()
    {
        if (useCustomCrosshair && customCrosshairSprite != null)
        {
            crosshairDot.sprite = customCrosshairSprite;
        }
        else
        {
            crosshairDot.sprite = CreateDiamondSprite();
        }
    }

    private Sprite CreateDiamondSprite()
    {
        int texSize = 64;
        Texture2D tex = new Texture2D(texSize, texSize);
        tex.filterMode = FilterMode.Trilinear;

        float halfSize = texSize * 0.5f;
        Vector2 center = new Vector2(halfSize, halfSize);
        float diamondSize = texSize * 0.15f;

        for (int x = 0; x < texSize; x++)
        {
            for (int y = 0; y < texSize; y++)
            {
                Vector2 pixelPos = new Vector2(x, y) - center;
                float rotX = (pixelPos.x - pixelPos.y) * 0.707107f;
                float rotY = (pixelPos.x + pixelPos.y) * 0.707107f;

                float alpha = (Mathf.Abs(rotX) < diamondSize && Mathf.Abs(rotY) < diamondSize) ? 1 : 0;
                tex.SetPixel(x, y, new Color(1, 1, 1, alpha));
            }
        }

        tex.Apply();
        return Sprite.Create(tex, new Rect(0, 0, texSize, texSize), new Vector2(0.5f, 0.5f));
    }
    
    private void CreateDefaultOverlaySprite()
    {
        if (overlayImage == null) return;
        
        // Create a circle texture for the overlay
        int texSize = 128;
        Texture2D tex = new Texture2D(texSize, texSize);
        tex.filterMode = FilterMode.Trilinear;
        
        float halfSize = texSize * 0.5f;
        Vector2 center = new Vector2(halfSize, halfSize);
        float outerRadius = halfSize - 2;
        float innerRadius = outerRadius - 8; // 8 pixel thick ring
        
        for (int x = 0; x < texSize; x++)
        {
            for (int y = 0; y < texSize; y++)
            {
                Vector2 pixelPos = new Vector2(x, y) - center;
                float distance = pixelPos.magnitude;
                
                // Create a ring shape
                if (distance <= outerRadius && distance >= innerRadius)
                {
                    // Add anti-aliasing at edges
                    float outerAlpha = Mathf.Clamp01(outerRadius - distance);
                    float innerAlpha = Mathf.Clamp01(distance - innerRadius);
                    float alpha = Mathf.Min(outerAlpha, innerAlpha);
                    
                    tex.SetPixel(x, y, new Color(1, 1, 1, alpha));
                }
                else
                {
                    tex.SetPixel(x, y, Color.clear);
                }
            }
        }
        
        tex.Apply();
        overlayImageSprite = Sprite.Create(tex, new Rect(0, 0, texSize, texSize), new Vector2(0.5f, 0.5f));
        overlayImage.sprite = overlayImageSprite;
        
        Debug.Log("[CrosshairManager] Created default overlay sprite (circle)");
    }

    #endregion

    #region Update Methods
    
    private void UpdateGrabHoldState()
    {
        bool wasGrabbingOrHolding = isGrabbingOrHolding;
        
        // Check if currently grabbing or heavy pulling
        // Note: You may need to add a public property for IsHeavyPulling in GrabInteraction
        bool isGrabbing = false;
        if (grabInteraction != null)
        {
            isGrabbing = grabInteraction.IsGrabbing;
            
            // Also check for heavy pulling if your GrabInteraction has that property
            // Uncomment this line if you add IsHeavyPulling property to GrabInteraction:
            isGrabbing = isGrabbing || grabInteraction.IsHeavyPulling;
        }
        
        // Combine grabbing and holding states
        isGrabbingOrHolding = isGrabbing || isCurrentlyHolding;
        
        // Update overlay visibility based on grab/hold state
        if (overlayImage != null)
        {
            bool shouldShowOverlay = (isGrabbing && showOverlayDuringGrab) || 
                                     (isCurrentlyHolding && showOverlayDuringHold);
            
            // Debug logging
            if (shouldShowOverlay && !overlayImage.gameObject.activeSelf)
            {
                Debug.Log($"[CrosshairManager] Showing overlay - Grabbing: {isGrabbing}, Holding: {isCurrentlyHolding}");
                if (overlayImageSprite == null)
                {
                    Debug.LogWarning("[CrosshairManager] Overlay sprite is not assigned! Creating default circle.");
                    CreateDefaultOverlaySprite();
                }
            }
            else if (!shouldShowOverlay && overlayImage.gameObject.activeSelf)
            {
                Debug.Log("[CrosshairManager] Hiding overlay");
            }
            
            overlayImage.gameObject.SetActive(shouldShowOverlay);
        }
        else
        {
            Debug.LogWarning("[CrosshairManager] Overlay image is null!");
        }
        
        // If state changed, update target opacity
        if (wasGrabbingOrHolding != isGrabbingOrHolding)
        {
            UpdateTargetOpacity();
        }
    }
    
    private bool ShouldUpdateRaycast()
    {
        float timeSinceLastRaycast = Time.time - lastRaycastTime;
        bool timeThresholdMet = timeSinceLastRaycast >= raycastUpdateInterval;
        
        float positionDelta = Vector3.Distance(playerCamera.transform.position, lastCameraPosition);
        bool positionThresholdMet = positionDelta > cameraMovementThreshold;
        
        float rotationDelta = Quaternion.Angle(playerCamera.transform.rotation, lastCameraRotation);
        bool rotationThresholdMet = rotationDelta > cameraRotationThreshold;
        
        return timeThresholdMet || positionThresholdMet || rotationThresholdMet;
    }
    
    private void CheckNearbyItems()
    {
        if (playerCamera == null) return;
        
        lastProximityCheckTime = Time.time;
        
        // Get player layer to exclude
        int playerLayer = LayerMask.NameToLayer("Player");
        LayerMask checkLayers = interactionLayers & ~(1 << playerLayer);
        
        // Perform sphere check
        int hitCount = Physics.OverlapSphereNonAlloc(
            playerCamera.transform.position,
            itemProximityRadius,
            nearbyColliders,
            checkLayers,
            QueryTriggerInteraction.Collide
        );
        
        bool foundItem = false;
        
        // Check each collider for InvItemPickup
        for (int i = 0; i < hitCount; i++)
        {
            if (nearbyColliders[i] == null) continue;
            
            // Skip player-related objects
            if (nearbyColliders[i].CompareTag("Player")) continue;
            
            // Check for item pickup component
            InvItemPickup itemPickup = nearbyColliders[i].GetComponent<InvItemPickup>();
            if (itemPickup == null)
            {
                itemPickup = nearbyColliders[i].GetComponentInParent<InvItemPickup>();
            }
            
            if (itemPickup != null)
            {
                foundItem = true;
                break;
            }
        }
        
        itemsNearby = foundItem;
        UpdateTargetOpacity();
    }
    
    private void PerformInteractionRaycast()
    {
        if (playerCamera == null) return;
        
        // Update tracking
        lastRaycastTime = Time.time;
        lastCameraPosition = playerCamera.transform.position;
        lastCameraRotation = playerCamera.transform.rotation;
        
        // Setup
        int playerLayer = LayerMask.NameToLayer("Player");
        LayerMask modifiedLayers = interactionLayers & ~(1 << playerLayer);
        Vector3 rayOrigin = playerCamera.transform.position + playerCamera.transform.forward * 0.2f;
        
        // Perform raycast
        int hitCount = Physics.RaycastNonAlloc(
            rayOrigin,
            playerCamera.transform.forward,
            raycastHits,
            interactionDetectionDistance,
            modifiedLayers,
            QueryTriggerInteraction.Collide
        );
        
        bool interactableDetected = false;
        bool itemDetected = false;
        
        // Process all hits
        for (int i = 0; i < hitCount; i++)
        {
            RaycastHit hit = raycastHits[i];
            
            // Skip player-related hits
            if (hit.collider.CompareTag("Player"))
                continue;
                
            // Check hierarchy for player (for very close hits)
            if (hit.distance < 0.3f)
            {
                Transform checkTransform = hit.collider.transform;
                bool isPlayer = false;
                while (checkTransform != null)
                {
                    if (checkTransform.CompareTag("Player"))
                    {
                        isPlayer = true;
                        break;
                    }
                    checkTransform = checkTransform.parent;
                }
                if (isPlayer) continue;
            }
            
            // Skip interactive screens (they handle their own crosshair)
            if (hit.collider.GetComponent<InteractiveWorldScreen>() != null)
                continue;
                
            // Skip if child of self
            if (hit.collider.gameObject.transform.IsChildOf(transform))
                continue;
            
            // Check for IInteractable (includes shops, stashes, etc.)
            if (!interactableDetected)
            {
                IInteractable interactable = hit.collider.GetComponent<IInteractable>();
                if (interactable == null)
                {
                    interactable = hit.collider.GetComponentInParent<IInteractable>();
                }
                
                if (interactable != null && interactable.CanInteract(gameObject))
                {
                    // Check if within interaction distance
                    float distance = Vector3.Distance(playerCamera.transform.position, interactable.gameObject.transform.position);
                    if (distance <= interactable.InteractionDistance)
                    {
                        interactableDetected = true;
                    }
                }
            }
            
            // Check for item
            if (!itemDetected)
            {
                InvItemPickup itemPickup = hit.collider.GetComponent<InvItemPickup>();
                if (itemPickup == null)
                {
                    itemPickup = hit.collider.GetComponentInParent<InvItemPickup>();
                }
                
                if (itemPickup != null)
                {
                    itemDetected = true;
                }
            }
            
            // Early exit if we found everything
            if (interactableDetected && itemDetected)
                break;
        }
        
        // Update states
        isLookingAtInteractable = interactableDetected;
        isLookingAtItem = itemDetected;
        UpdateTargetOpacity();
    }
    
    private void UpdateTargetOpacity()
    {
        // Determine target opacity based on priority
        if (isGrabbingOrHolding)
        {
            // ALWAYS show at full opacity during grab/hold - this takes priority
            targetCrosshairOpacity = interactableMaxOpacity;
            currentCrosshairOpacity = interactableMaxOpacity; // Force immediate full opacity
        }
        else if (isLookingAtInteractable || isLookingAtItem)
        {
            // Looking directly at something interactable
            targetCrosshairOpacity = interactableMaxOpacity;
        }
        else if (itemsNearby && enableItemProximity)
        {
            // Items nearby but not looking at them
            targetCrosshairOpacity = itemProximityOpacity;
        }
        else
        {
            // Nothing to interact with
            targetCrosshairOpacity = 0f;
        }
        
        UpdateCrosshairVisibility();
    }
    
    private void UpdateCrosshairFade()
    {
        if (crosshairDot == null) return;
        
        // Smoothly fade the opacity
        currentCrosshairOpacity = Mathf.Lerp(
            currentCrosshairOpacity, 
            targetCrosshairOpacity, 
            Time.deltaTime * crosshairFadeSpeed
        );
        
        // Apply the opacity to the crosshair
        if (currentCrosshairOpacity > 0.01f || alwaysShowCrosshair || forceCrosshairVisible)
        {
            Color currentColor = baseCrosshairColor;
            
            // Use full opacity if forced or always shown, otherwise use calculated opacity
            if (forceCrosshairVisible || alwaysShowCrosshair)
            {
                currentColor.a = baseCrosshairColor.a;
            }
            else
            {
                currentColor.a = baseCrosshairColor.a * currentCrosshairOpacity;
            }
            
            crosshairDot.color = currentColor;
        }
    }

    private void UpdateCrosshairAppearance()
    {
        if (crosshairDot == null) return;

        float scaleFactor = screenScaleCompensation ? Screen.height / baseScreenHeight : 1f;
        float scaledSize = crosshairSize * scaleFactor;

        baseCrosshairColor = crosshairColor;
        crosshairDot.color = crosshairColor;

        if (useCustomCrosshair && customCrosshairSprite != null)
        {
            crosshairDot.rectTransform.sizeDelta = new Vector2(crosshairSize, crosshairSize);
            crosshairDot.preserveAspect = true;
        }
        else
        {
            crosshairDot.rectTransform.sizeDelta = new Vector2(scaledSize, scaledSize);
        }

        SetRectTransformToCenter(crosshairDot.rectTransform);
    }
    
    private void UpdateOverlayAppearance()
    {
        if (overlayImage == null) return;
        
        // If still no sprite, create default
        if (overlayImageSprite == null)
        {
            CreateDefaultOverlaySprite();
        }
        
        if (overlayImageSprite == null) return; // Still no sprite somehow

        overlayImage.sprite = overlayImageSprite;

        Rect spriteRect = overlayImageSprite.rect;
        float aspectRatio = spriteRect.width / spriteRect.height;
        float width = overlaySize.x;
        float height = overlaySize.y;

        if (width / height > aspectRatio)
        {
            width = height * aspectRatio;
        }
        else
        {
            height = width / aspectRatio;
        }

        overlayImage.color = overlayColor;
        overlayImage.rectTransform.sizeDelta = new Vector2(width, height);

        SetRectTransformToCenter(overlayImage.rectTransform);
    }

    private void SetRectTransformToCenter(RectTransform rectTransform)
    {
        rectTransform.anchorMin = new Vector2(0.5f, 0.5f);
        rectTransform.anchorMax = new Vector2(0.5f, 0.5f);
        rectTransform.pivot = new Vector2(0.5f, 0.5f);
        rectTransform.anchoredPosition = Vector2.zero;
    }
    
    private void UpdateCrosshairVisibility()
    {
        if (crosshairDot != null)
        {
            // Show crosshair if not looking at screen AND should be visible
            bool shouldShow = !isLookingAtScreen && 
                             (forceCrosshairVisible || 
                              alwaysShowCrosshair || 
                              isGrabbingOrHolding ||
                              currentCrosshairOpacity > 0.01f);
            
            crosshairDot.gameObject.SetActive(shouldShow);
        }
    }

    #endregion

    #region Public Methods

    public void SetCrosshairType(bool useCustom)
    {
        useCustomCrosshair = useCustom;
        UpdateCrosshairTextures();
        UpdateCrosshairAppearance();
    }

    public void SetCustomCrosshairSprite(Sprite newSprite)
    {
        customCrosshairSprite = newSprite;
        if (useCustomCrosshair)
        {
            UpdateCrosshairTextures();
            UpdateCrosshairAppearance();
        }
    }
    
    public void SetCrosshairOpacityExternal(float newOpacity)
    {
        forceCrosshairVisible = newOpacity > 0.01f;
        UpdateCrosshairVisibility();
    }
    
    /// <summary>
    /// Called by InteractiveWorldScreen when player looks at/away from screen
    /// </summary>
    public void SetScreenLookState(bool lookingAtScreen)
    {
        isLookingAtScreen = lookingAtScreen;
        UpdateCrosshairVisibility();
        
        // Also hide overlay when looking at screen
        if (isLookingAtScreen && overlayImage != null)
        {
            overlayImage.gameObject.SetActive(false);
        }
        else
        {
            // Re-enable overlay if we're grabbing/holding
            UpdateGrabHoldState();
        }
    }
    
    /// <summary>
    /// Force show the crosshair (useful for special interactions)
    /// </summary>
    public void ForceShowCrosshair(bool show)
    {
        forceCrosshairVisible = show;
        UpdateCrosshairVisibility();
    }
    
    /// <summary>
    /// Set hold interaction state (call this from your hold interaction system)
    /// </summary>
    public void SetHoldInteractionState(bool isHolding)
    {
        if (isHolding != isCurrentlyHolding)
        {
            isCurrentlyHolding = isHolding;
            
            // Update overlay visibility
            if (overlayImage != null && showOverlayDuringHold)
            {
                overlayImage.gameObject.SetActive(isHolding);
            }
            
            UpdateTargetOpacity();
        }
    }
    
    /// <summary>
    /// Debug method to test overlay visibility
    /// </summary>
    [ContextMenu("Test Show Overlay")]
    public void TestShowOverlay()
    {
        if (overlayImage == null)
        {
            Debug.LogError("[CrosshairManager] Overlay image is null! Setup might have failed.");
            return;
        }
        
        if (overlayImageSprite == null)
        {
            Debug.LogWarning("[CrosshairManager] No overlay sprite assigned, creating default");
            CreateDefaultOverlaySprite();
        }
        
        overlayImage.gameObject.SetActive(true);
        Debug.Log($"[CrosshairManager] Test: Overlay should now be visible. Active: {overlayImage.gameObject.activeSelf}");
    }
    
    /// <summary>
    /// Debug method to test overlay visibility
    /// </summary>
    [ContextMenu("Test Hide Overlay")]
    public void TestHideOverlay()
    {
        if (overlayImage != null)
        {
            overlayImage.gameObject.SetActive(false);
            Debug.Log("[CrosshairManager] Test: Overlay hidden");
        }
    }

    #endregion

    #if UNITY_EDITOR
    private void OnDrawGizmos()
    {
        if (playerCamera != null)
        {
            Vector3 rayOrigin = playerCamera.transform.position + playerCamera.transform.forward * 0.2f;
            
            // Draw interaction ray
            Gizmos.color = isLookingAtInteractable ? Color.green : 
                          isLookingAtItem ? Color.cyan : 
                          Color.yellow;
            Gizmos.DrawRay(rayOrigin, playerCamera.transform.forward * interactionDetectionDistance);
            
            // Draw interaction distance marker
            Gizmos.color = Color.blue;
            Gizmos.DrawWireSphere(rayOrigin + playerCamera.transform.forward * interactionDetectionDistance, 0.05f);
            
            // Draw item proximity sphere
            if (enableItemProximity)
            {
                Gizmos.color = itemsNearby ? new Color(0, 1, 1, 0.2f) : new Color(0, 0, 1, 0.1f);
                Gizmos.DrawWireSphere(playerCamera.transform.position, itemProximityRadius);
            }
        }
    }
    #endif
}