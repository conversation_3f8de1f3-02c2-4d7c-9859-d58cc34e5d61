using UnityEngine;
using KinematicCharacterController;
using System.Collections.Generic;

[RequireComponent(typeof(Collider))]
public class KinematicPlatformPassengerZone : MonoBehaviour
{
    [Header("Platform Reference")]
    [Tooltip("Platform that will receive passenger register/unregister callbacks")] 
    [SerializeField] private KinematicPlatform platform;

    [<PERSON><PERSON>("Attachment Settings")]
    [Tooltip("Only affect KinematicCharacterMotor objects")] 
    [SerializeField] private bool onlyAffectKCC = false;
    
    [Tooltip("Parent items to platform attachment anchor for perfect attachment at high speeds")]
    [SerializeField] private bool parentItemsToPlatform = true;
    
    [Tooltip("Keep passengers attached for this many seconds after leaving the trigger")]
    [SerializeField] private float detachGraceSeconds = 0.75f;
    
    [Tooltip("Enable dynamic grace time adjustment based on velocities")] 
    [SerializeField] private bool useDynamicGrace = true;
    
    [SerializeField] private float velocityGraceMultiplier = 0.1f;
    [SerializeField] private float maxGraceTime = 2f;
    
    [Head<PERSON>("High Speed Settings")]
    [Tooltip("Force attachment for players at high speeds")]
    [SerializeField] private bool forceAttachmentAtHighSpeed = true;
    
    [Tooltip("Speed threshold for forced attachment")]
    [SerializeField] private float highSpeedThreshold = 10f;
    
    [Tooltip("Extra attachment time at high speeds")]
    [SerializeField] private float highSpeedExtraGrace = 1f;

    // NEW: Speed-based grace scaling
    [Tooltip("Scale grace period with platform speed")] 
    [SerializeField] private bool scaleGraceWithSpeed = true;
    
    [Tooltip("Additional grace per 100 m/s of platform speed")] 
    [SerializeField] private float gracePerHundredSpeed = 0.5f;

    private Collider _collider;
    private Transform _platformTransform;
    private Transform _attachmentAnchor;
    private PhysicsMover _platformMover;
    private CapsuleCollider _playerCapsule;
    
    // Track all passengers with more detail
    private class PassengerInfo
    {
        public Component component;
        public float lastSeenTime;
        public Transform originalParent;
        public bool isParented;
        public Vector3 localPosition;
        public Quaternion localRotation;
        public bool wasKinematic;
        public RigidbodyConstraints originalConstraints;
        public List<Collider> cachedColliders;
        public bool collisionsIgnoredWithPlayer;
        public List<bool> originalIsTrigger;
        public List<bool> originalEnabled;
    }
    
    private readonly Dictionary<Component, PassengerInfo> _passengers = new Dictionary<Component, PassengerInfo>();
    private readonly List<Component> _toRemove = new List<Component>();
    
    // Track paused state
    private bool _isPaused = false;

    private void Reset()
    {
        EnsureSetup();
    }

    private void OnValidate()
    {
        EnsureSetup();
    }

    private void Awake()
    {
        EnsureSetup();
    }

    private void EnsureSetup()
    {
        if (platform == null)
        {
            platform = GetComponentInParent<KinematicPlatform>();
        }
        
        if (platform != null)
        {
            _platformTransform = platform.transform;
            _platformMover = platform.GetComponent<PhysicsMover>();
            _attachmentAnchor = platform.AttachmentAnchor;
        }
        
        _collider = GetComponent<Collider>();
        if (_collider != null)
        {
            _collider.isTrigger = true;
        }

        // Cache player capsule once for collision toggling
        if (_playerCapsule == null)
        {
            var motor = FindFirstObjectByType<KinematicCharacterMotor>();
            if (motor != null)
            {
                _playerCapsule = motor.Capsule;
            }
        }
    }

    private void OnTriggerEnter(Collider other)
    {
        if (platform == null) return;
        if (PersistenceManager.IsRestoring) return; // Do not modify attachments while restoring
        
        // Handle KinematicCharacterMotor
        var motor = other.GetComponentInParent<KinematicCharacterMotor>();
        if (motor != null)
        {
            RegisterMotor(motor);
        }
        
        // Handle regular rigidbodies (items)
        if (!onlyAffectKCC)
        {
            var rb = other.attachedRigidbody ?? other.GetComponentInParent<Rigidbody>();
            if (rb != null && rb != platform.GetComponent<Rigidbody>() && !IsPlayerRelatedRigidbody(rb))
            {
                RegisterRigidbody(rb);
            }
        }
    }

    private void RegisterMotor(KinematicCharacterMotor motor)
    {
        if (_passengers.ContainsKey(motor)) return;
        if (PersistenceManager.IsRestoring) return; // Avoid altering motor attachment during restore
        
        var info = new PassengerInfo
        {
            component = motor,
            lastSeenTime = Time.time,
            originalParent = motor.transform.parent,
            isParented = false
        };
        
        _passengers[motor] = info;
        platform.RegisterPassenger(motor);
        
        // Force attachment at high speeds
        if (forceAttachmentAtHighSpeed && IsMovingFast())
        {
            motor.AttachedRigidbodyOverride = platform.GetComponent<Rigidbody>();
        }
    }

    private void RegisterRigidbody(Rigidbody rb)
    {
        if (_passengers.ContainsKey(rb)) return;
        if (IsPlayerRelatedRigidbody(rb)) return; // Never register player-owned rigidbodies as items
        if (PersistenceManager.IsRestoring) return; // Avoid parenting/physics changes during restore
        
        var info = new PassengerInfo
        {
            component = rb,
            lastSeenTime = Time.time,
            originalParent = rb.transform.parent,
            isParented = false,
            wasKinematic = rb.isKinematic,
            originalConstraints = rb.constraints
        };
        
        _passengers[rb] = info;
        platform.RegisterPassengerRigidbody(rb);
        
        // Don't parent immediately - only when platform starts moving
    }

    private bool ShouldParentItem(Rigidbody rb)
    {
        // Check if it's grounded on the platform
        RaycastHit hit;
        if (Physics.Raycast(rb.position, Vector3.down, out hit, 2f))
        {
            if (hit.collider.transform.IsChildOf(_platformTransform) || 
                hit.collider.transform == _platformTransform)
            {
                return true;
            }
        }
        
        return false;
    }

    private void ParentToPlatform(Rigidbody rb, PassengerInfo info)
    {
        if (info.isParented) return;
        
        // Store local position before parenting (relative to anchor for clean inheritance)
        var parentRef = _attachmentAnchor != null ? _attachmentAnchor : _platformTransform;
        info.localPosition = parentRef.InverseTransformPoint(rb.position);
        info.localRotation = Quaternion.Inverse(parentRef.rotation) * rb.rotation;
        
        // Parent the object to the anchor (which has identity scale/rotation)
        rb.transform.SetParent(parentRef);
        
        // Make it kinematic while parented but preserve original mass/drag
        rb.isKinematic = true;
        rb.constraints = RigidbodyConstraints.FreezeAll;
        
		// For small pickup items and heavy objects: disable physical interaction while parented
		if (IsSmallItem(rb) || IsHeavyObject(rb))
        {
            TogglePlayerCollisionWithItem(rb, info, true);
            SetItemCollidersAsTrigger(rb, info, true);
        }

        info.isParented = true;
    }

    private void UnparentFromPlatform(Rigidbody rb, PassengerInfo info)
    {
        if (!info.isParented || rb == null) return;
        
        // Calculate world velocity from platform movement
        Vector3 platformVelocity = Vector3.zero;
        Vector3 angularVelocity = Vector3.zero;
        
        if (_platformMover != null)
        {
            platformVelocity = _platformMover.Velocity;
            angularVelocity = _platformMover.AngularVelocity;
            
            // Add rotational velocity component - use transform position to avoid destroyed rigidbody access
            Vector3 relativePos = rb.transform.position - _platformTransform.position;
            platformVelocity += Vector3.Cross(angularVelocity, relativePos);
        }
        
        // Unparent back to world items container if available, else original parent
        Transform targetParent = info.originalParent;
        if (WorldItemManager.Instance != null && WorldItemManager.Instance.ItemsContainer != null)
        {
            targetParent = WorldItemManager.Instance.ItemsContainer;
        }
        rb.transform.SetParent(targetParent);
        
        // Restore physics settings
        rb.isKinematic = info.wasKinematic;
        rb.constraints = info.originalConstraints;
        
        // Apply platform velocity to maintain momentum
        if (!rb.isKinematic)
        {
            rb.linearVelocity = platformVelocity;
            rb.angularVelocity = angularVelocity;
        }
        
		// For small pickup items and heavy objects: restore collisions and collider states
		if (IsSmallItem(rb) || IsHeavyObject(rb))
        {
            TogglePlayerCollisionWithItem(rb, info, false);
            SetItemCollidersAsTrigger(rb, info, false);
        }
        
        info.isParented = false;
    }

    private void OnTriggerStay(Collider other)
    {
        if (platform == null) return;
        if (PersistenceManager.IsRestoring) return; // Skip dynamic attachment/parenting during restore
        
        float now = Time.time;
        
        // Update motor
        var motor = other.GetComponentInParent<KinematicCharacterMotor>();
        // Auto-register motors that started inside the trigger
        if (motor != null && !_passengers.ContainsKey(motor))
        {
            RegisterMotor(motor);
        }
        if (motor != null && _passengers.ContainsKey(motor))
        {
            _passengers[motor].lastSeenTime = now;
            
            // Ensure attachment at high speeds
            if (forceAttachmentAtHighSpeed && IsMovingFast())
            {
                var platformRb = platform.GetComponent<Rigidbody>();
                if (motor.AttachedRigidbodyOverride != platformRb)
                {
                    motor.AttachedRigidbodyOverride = platformRb;
                }
            }

            // Do not release platform hold here; PersistenceManager controls release
        }
        
        // Update rigidbody
        if (!onlyAffectKCC)
        {
            var rb = other.attachedRigidbody ?? other.GetComponentInParent<Rigidbody>();
            if (rb != null && rb != platform.GetComponent<Rigidbody>() && !IsPlayerRelatedRigidbody(rb))
            {
                // Auto-register rigidbodies that started inside the trigger
                if (!_passengers.ContainsKey(rb))
                {
                    RegisterRigidbody(rb);
                }

                if (_passengers.ContainsKey(rb))
                {
                    _passengers[rb].lastSeenTime = now;
                    
                    // Check if rigidbody (item or heavy object) should be parented when platform is moving
                    if (parentItemsToPlatform && !_passengers[rb].isParented && ShouldParentItem(rb) && platform.IsMoving)
                    {
                        ParentToPlatform(rb, _passengers[rb]);
                    }
                    // Unparent when platform stops moving
                    else if (_passengers[rb].isParented && !platform.IsMoving)
                    {
                        UnparentFromPlatform(rb, _passengers[rb]);
                    }
                }
            }
        }
    }

    // Exclude any rigidbody that belongs to the player hierarchy
    private bool IsPlayerRelatedRigidbody(Rigidbody rb)
    {
        if (rb == null) return false;
        return rb.GetComponentInParent<KinematicCharacterMotor>() != null;
    }

    private void OnTriggerExit(Collider other)
    {
        // Mark last seen time but don't immediately detach (grace period)
        var motor = other.GetComponentInParent<KinematicCharacterMotor>();
        if (motor != null && _passengers.ContainsKey(motor))
        {
            _passengers[motor].lastSeenTime = Time.time;
        }
        
        var rb = other.attachedRigidbody ?? other.GetComponentInParent<Rigidbody>();
        if (rb != null && _passengers.ContainsKey(rb))
        {
            _passengers[rb].lastSeenTime = Time.time;
        }
    }

    private void Update()
    {
        if (platform == null) return;
        if (_passengers.Count == 0) return;
        if (PersistenceManager.IsRestoring) return; // Do not detach/attach while restoring
        
        float now = Time.time;
        _toRemove.Clear();
        
        foreach (var kvp in _passengers)
        {
            var info = kvp.Value;
            if (info.component == null)
            {
                _toRemove.Add(kvp.Key);
                continue;
            }
            
            float gracePeriod = CalculateGracePeriod(info.component);
            
            if (now - info.lastSeenTime > gracePeriod)
            {
                _toRemove.Add(kvp.Key);
            }
        }
        
        // Remove expired passengers
        foreach (var comp in _toRemove)
        {
            RemovePassenger(comp);
        }
    }

    private float CalculateGracePeriod(Component passenger)
    {
        float grace = detachGraceSeconds;
        
        // Add extra grace for high speed
        if (IsMovingFast())
        {
            grace += highSpeedExtraGrace;
        }
        
        // NEW: Scale with platform speed
        if (scaleGraceWithSpeed && _platformMover != null)
        {
            float platformSpeed = _platformMover.Velocity.magnitude;
            grace += (platformSpeed / 100f) * gracePerHundredSpeed;
        }

        // Dynamic grace based on velocities
        if (useDynamicGrace && _platformMover != null)
        {
            float platformSpeed = _platformMover.Velocity.magnitude;
            grace += platformSpeed * velocityGraceMultiplier;
            
            // Check passenger velocity
            if (passenger is KinematicCharacterMotor motor)
            {
                grace += motor.Velocity.magnitude * velocityGraceMultiplier * 0.5f;
            }
            else if (passenger is Rigidbody rb && !rb.isKinematic)
            {
                grace += rb.linearVelocity.magnitude * velocityGraceMultiplier * 0.5f;
            }
        }
        
        return Mathf.Min(grace, maxGraceTime);
    }

    private void RemovePassenger(Component comp)
    {
        if (!_passengers.TryGetValue(comp, out var info)) return;
        
        if (comp is KinematicCharacterMotor motor)
        {
            platform.UnregisterPassenger(motor);
            
            // Clear forced attachment
            if (motor.AttachedRigidbodyOverride == platform.GetComponent<Rigidbody>())
            {
                motor.AttachedRigidbodyOverride = null;
            }
            
            // No vault suppression toggling here anymore
        }
        else if (comp is Rigidbody rb)
        {
            // Unparent if needed - check if rigidbody still exists
            if (info.isParented && rb != null)
            {
                UnparentFromPlatform(rb, info);
            }
            else if (rb != null)
            {
				// Ensure collisions are restored even if already unparented (small items and heavy objects)
				if (IsSmallItem(rb) || IsHeavyObject(rb))
                {
                    TogglePlayerCollisionWithItem(rb, info, false);
                    SetItemCollidersAsTrigger(rb, info, false);
                }
            }
            
            platform.UnregisterPassengerRigidbody(rb);
        }
        
        _passengers.Remove(comp);
    }

    private bool IsMovingFast()
    {
        if (_platformMover == null) return false;
        return _platformMover.Velocity.magnitude > highSpeedThreshold;
    }

    public void SetPaused(bool paused)
    {
        _isPaused = paused;
        
        if (paused)
        {
            // Immediately stop platform movement
            if (platform != null)
            {
                platform.SetSleepMode(true);
            }
        }
        else
        {
            if (platform != null)
            {
                platform.SetSleepMode(false);
            }
        }
    }

    private void LateUpdate()
    {
        // During restoration, actively zero out unexpected additive velocities for motors inside zone
        if (!PersistenceManager.IsRestoring)
        {
            return;
        }
        if (_passengers.Count == 0) return;

        foreach (var kv in _passengers)
        {
            if (kv.Key is KinematicCharacterMotor motor)
            {
                // Prevent sudden impulses by canceling base velocity while restoring
                motor.BaseVelocity = Vector3.zero;
                // Keep them attached to platform rigidbody to avoid relative drift
                var rb = platform != null ? platform.GetComponent<Rigidbody>() : null;
                if (rb != null && motor.AttachedRigidbodyOverride != rb)
                {
                    motor.AttachedRigidbodyOverride = rb;
                }
            }
            else if (kv.Key is Rigidbody rb)
            {
                // Freeze dynamic rigidbodies influence while restoring
                if (!rb.isKinematic)
                {
                    rb.linearVelocity = Vector3.zero;
                    rb.angularVelocity = Vector3.zero;
                }
				// Also ensure collisions with player remain ignored while restoring if we parented
				if (_passengers.TryGetValue(rb, out var pInfo) && pInfo.isParented && (IsSmallItem(rb) || IsHeavyObject(rb)))
                {
                    TogglePlayerCollisionWithItem(rb, pInfo, true);
                }
            }
        }
    }

    private void OnDestroy()
    {
        // Clean up all passengers
        _toRemove.Clear();
        _toRemove.AddRange(_passengers.Keys);
        
        foreach (var comp in _toRemove)
        {
            RemovePassenger(comp);
        }
        
        _passengers.Clear();
    }

    private void OnDrawGizmosSelected()
    {
        if (_collider == null) return;
        
        // Draw the trigger zone
        Gizmos.color = new Color(0, 1, 0, 0.3f);
        
        if (_collider is BoxCollider box)
        {
            Matrix4x4 oldMatrix = Gizmos.matrix;
            Gizmos.matrix = Matrix4x4.TRS(transform.position, transform.rotation, transform.lossyScale);
            Gizmos.DrawCube(box.center, box.size);
            Gizmos.color = Color.green;
            Gizmos.DrawWireCube(box.center, box.size);
            Gizmos.matrix = oldMatrix;
        }
        
        // Draw passenger count
        if (Application.isPlaying && _passengers.Count > 0)
        {
            Gizmos.color = Color.yellow;
            foreach (var info in _passengers.Values)
            {
                if (info.component != null)
                {
                    Gizmos.DrawLine(transform.position, info.component.transform.position);
                }
            }
        }
    }

    // Toggle collision between the player's capsule and all colliders on the item
    private void TogglePlayerCollisionWithItem(Rigidbody rb, PassengerInfo info, bool ignore)
    {
        if (_playerCapsule == null || rb == null)
        {
            return;
        }

        // Lazily cache item colliders
        if (info.cachedColliders == null || info.cachedColliders.Count == 0)
        {
            info.cachedColliders = new List<Collider>(rb.GetComponentsInChildren<Collider>(true));
        }
        if (info.cachedColliders == null)
        {
            return;
        }

        // Avoid redundant toggles
        if (ignore && info.collisionsIgnoredWithPlayer) return;
        if (!ignore && !info.collisionsIgnoredWithPlayer) return;

        for (int i = 0; i < info.cachedColliders.Count; i++)
        {
            var col = info.cachedColliders[i];
            if (col == null) continue;
            if (col.isTrigger) continue;
            Physics.IgnoreCollision(_playerCapsule, col, ignore);
        }

        info.collisionsIgnoredWithPlayer = ignore;
    }

    private bool IsSmallItem(Rigidbody rb)
    {
        return rb != null && rb.GetComponent<InvItemPickup>() != null;
    }

    private bool IsHeavyObject(Rigidbody rb)
    {
        return rb != null && rb.GetComponent<HeavyObjectManager>() != null;
    }

    // Set or restore item colliders to trigger mode while parented, restore on unparent
    private void SetItemCollidersAsTrigger(Rigidbody rb, PassengerInfo info, bool asTrigger)
    {
        if (rb == null) return;

        if (info.cachedColliders == null || info.cachedColliders.Count == 0)
        {
            info.cachedColliders = new List<Collider>(rb.GetComponentsInChildren<Collider>(true));
        }
        if (info.cachedColliders == null || info.cachedColliders.Count == 0) return;

        if (asTrigger)
        {
            // Cache originals once
            if (info.originalIsTrigger == null || info.originalIsTrigger.Count != info.cachedColliders.Count)
            {
                info.originalIsTrigger = new List<bool>(info.cachedColliders.Count);
                info.originalEnabled = new List<bool>(info.cachedColliders.Count);
                info.originalIsTrigger.Clear();
                info.originalEnabled.Clear();
                for (int i = 0; i < info.cachedColliders.Count; i++)
                {
                    var col = info.cachedColliders[i];
                    if (col == null)
                    {
                        info.originalIsTrigger.Add(true);
                        info.originalEnabled.Add(false);
                        continue;
                    }
                    info.originalIsTrigger.Add(col.isTrigger);
                    info.originalEnabled.Add(col.enabled);
                }
            }

            for (int i = 0; i < info.cachedColliders.Count; i++)
            {
                var col = info.cachedColliders[i];
                if (col == null) continue;
                // Keep collider enabled but make it a trigger to avoid physical response
                col.isTrigger = true;
            }
        }
        else
        {
            // Restore original states if available
            if (info.originalIsTrigger != null && info.originalEnabled != null &&
                info.originalIsTrigger.Count == info.cachedColliders.Count &&
                info.originalEnabled.Count == info.cachedColliders.Count)
            {
                for (int i = 0; i < info.cachedColliders.Count; i++)
                {
                    var col = info.cachedColliders[i];
                    if (col == null) continue;
                    col.isTrigger = info.originalIsTrigger[i];
                    col.enabled = info.originalEnabled[i];
                }
            }
        }
    }
}