using UnityEngine;
using KinematicCharacterController;
using System.Collections.Generic;

[RequireComponent(typeof(Collider))]
public class KinematicPlatformPassengerZone : MonoBehaviour
{
    [Header("Platform Reference")]
    [Tooltip("Platform that will receive passenger register/unregister callbacks")] 
    [SerializeField] private KinematicPlatform platform;

    [<PERSON><PERSON>("Attachment Settings")]
    [Tooltip("Only affect KinematicCharacterMotor objects")] 
    [SerializeField] private bool onlyAffectKCC = false;
    
    [Tooltip("Parent items to platform attachment anchor for perfect attachment at high speeds")]
    [SerializeField] private bool parentItemsToPlatform = true;
    
    [Tooltip("Keep passengers attached for this many seconds after leaving the trigger")]
    [SerializeField] private float detachGraceSeconds = 0.75f;
    
    [Tooltip("Enable dynamic grace time adjustment based on velocities")] 
    [SerializeField] private bool useDynamicGrace = true;
    
    [SerializeField] private float velocityGraceMultiplier = 0.1f;
    [SerializeField] private float maxGraceTime = 2f;
    
    [Header("High Speed Settings")]
    [Tooltip("Force attachment for players at high speeds")]
    [SerializeField] private bool forceAttachmentAtHighSpeed = true;
    
    [Tooltip("Speed threshold for forced attachment")]
    [SerializeField] private float highSpeedThreshold = 10f;
    
    [Tooltip("Extra attachment time at high speeds")]
    [SerializeField] private float highSpeedExtraGrace = 1f;

    [Tooltip("Scale grace period with platform speed")] 
    [SerializeField] private bool scaleGraceWithSpeed = true;
    
    [Tooltip("Additional grace per 100 m/s of platform speed")] 
    [SerializeField] private float gracePerHundredSpeed = 0.5f;

    private Collider _collider;
    private Transform _platformTransform;
    private Transform _attachmentAnchor;
    private PhysicsMover _platformMover;
    private CapsuleCollider _playerCapsule;
    
    private class PassengerInfo
    {
        public Component component;
        public float lastSeenTime;
        public Transform originalParent;
        public bool isParented;
        public Vector3 localPosition;
        public Quaternion localRotation;
        public bool wasKinematic;
        public RigidbodyConstraints originalConstraints;
        public List<Collider> cachedColliders;
        public bool collisionsIgnoredWithPlayer;
        public List<bool> originalIsTrigger;
        public List<bool> originalEnabled;
    }
    
    private readonly Dictionary<Component, PassengerInfo> _passengers = new Dictionary<Component, PassengerInfo>();
    private readonly List<Component> _toRemove = new List<Component>();
    
    private bool _isPaused = false;

    private void Reset()
    {
        EnsureSetup();
    }

    private void OnValidate()
    {
        EnsureSetup();
    }

    private void Awake()
    {
        EnsureSetup();
    }

    private void EnsureSetup()
    {
        if (platform == null)
        {
            platform = GetComponentInParent<KinematicPlatform>();
        }
        
        if (platform != null)
        {
            _platformTransform = platform.transform;
            _platformMover = platform.GetComponent<PhysicsMover>();
            _attachmentAnchor = platform.AttachmentAnchor;
        }
        
        _collider = GetComponent<Collider>();
        if (_collider != null)
        {
            _collider.isTrigger = true;
        }

        if (_playerCapsule == null)
        {
            var motor = FindFirstObjectByType<KinematicCharacterMotor>();
            if (motor != null)
            {
                _playerCapsule = motor.Capsule;
            }
        }
    }

    private void OnTriggerEnter(Collider other)
    {
        if (platform == null) return;
        if (PersistenceManager.IsRestoring) return;
        
        // Check for drop immunity on items
        if (other.attachedRigidbody != null)
        {
            GameObject rbGO = other.attachedRigidbody.gameObject;
            if (InvItemDropping.IsItemDropImmune(rbGO))
            {
                // Item was just dropped, don't capture it yet
                return;
            }
        }
        
        // Handle KinematicCharacterMotor
        var motor = other.GetComponentInParent<KinematicCharacterMotor>();
        if (motor != null)
        {
            RegisterMotor(motor);
        }
        
        // Handle regular rigidbodies (items)
        if (!onlyAffectKCC)
        {
            var rb = other.attachedRigidbody ?? other.GetComponentInParent<Rigidbody>();
            if (rb != null && rb != platform.GetComponent<Rigidbody>() && !IsPlayerRelatedRigidbody(rb))
            {
                RegisterRigidbody(rb);
            }
        }
    }

    private void RegisterMotor(KinematicCharacterMotor motor)
    {
        if (_passengers.ContainsKey(motor)) return;
        if (PersistenceManager.IsRestoring) return;
        
        var info = new PassengerInfo
        {
            component = motor,
            lastSeenTime = Time.time,
            originalParent = motor.transform.parent,
            isParented = false
        };
        
        _passengers[motor] = info;
        platform.RegisterPassenger(motor);
        
        if (forceAttachmentAtHighSpeed && IsMovingFast())
        {
            motor.AttachedRigidbodyOverride = platform.GetComponent<Rigidbody>();
        }
    }

    private void RegisterRigidbody(Rigidbody rb)
    {
        if (_passengers.ContainsKey(rb)) return;
        if (IsPlayerRelatedRigidbody(rb)) return;
        if (PersistenceManager.IsRestoring) return;
        
        // Double-check drop immunity
        if (InvItemDropping.IsItemDropImmune(rb.gameObject))
        {
            return;
        }
        
        var info = new PassengerInfo
        {
            component = rb,
            lastSeenTime = Time.time,
            originalParent = rb.transform.parent,
            isParented = false,
            wasKinematic = rb.isKinematic,
            originalConstraints = rb.constraints
        };
        
        _passengers[rb] = info;
        platform.RegisterPassengerRigidbody(rb);
    }

    private bool ShouldParentItem(Rigidbody rb)
    {
        // Don't parent items with drop immunity
        if (InvItemDropping.IsItemDropImmune(rb.gameObject))
        {
            return false;
        }
        
        // Check if it's grounded on the platform
        RaycastHit hit;
        if (Physics.Raycast(rb.position, Vector3.down, out hit, 2f))
        {
            if (hit.collider.transform.IsChildOf(_platformTransform) || 
                hit.collider.transform == _platformTransform)
            {
                return true;
            }
        }
        
        return false;
    }

    private void ParentToPlatform(Rigidbody rb, PassengerInfo info)
    {
        if (info.isParented) return;
        
        // Final immunity check before parenting
        if (InvItemDropping.IsItemDropImmune(rb.gameObject))
        {
            return;
        }
        
        var parentRef = _attachmentAnchor != null ? _attachmentAnchor : _platformTransform;
        info.localPosition = parentRef.InverseTransformPoint(rb.position);
        info.localRotation = Quaternion.Inverse(parentRef.rotation) * rb.rotation;
        
        rb.transform.SetParent(parentRef);
        
        rb.isKinematic = true;
        rb.constraints = RigidbodyConstraints.FreezeAll;
        
		if (IsSmallItem(rb) || IsHeavyObject(rb))
        {
            TogglePlayerCollisionWithItem(rb, info, true);
            SetItemCollidersAsTrigger(rb, info, true);
        }

        info.isParented = true;
    }

    private void UnparentFromPlatform(Rigidbody rb, PassengerInfo info)
    {
        if (!info.isParented || rb == null) return;
        
        Vector3 platformVelocity = Vector3.zero;
        Vector3 angularVelocity = Vector3.zero;
        
        if (_platformMover != null)
        {
            platformVelocity = _platformMover.Velocity;
            angularVelocity = _platformMover.AngularVelocity;
            
            Vector3 relativePos = rb.transform.position - _platformTransform.position;
            platformVelocity += Vector3.Cross(angularVelocity, relativePos);
        }
        
        Transform targetParent = info.originalParent;
        if (WorldItemManager.Instance != null && WorldItemManager.Instance.ItemsContainer != null)
        {
            targetParent = WorldItemManager.Instance.ItemsContainer;
        }
        rb.transform.SetParent(targetParent);
        
        rb.isKinematic = info.wasKinematic;
        rb.constraints = info.originalConstraints;
        
        if (!rb.isKinematic)
        {
            rb.linearVelocity = platformVelocity;
            rb.angularVelocity = angularVelocity;
        }
        
		if (IsSmallItem(rb) || IsHeavyObject(rb))
        {
            TogglePlayerCollisionWithItem(rb, info, false);
            SetItemCollidersAsTrigger(rb, info, false);
        }
        
        info.isParented = false;
    }

    private void OnTriggerStay(Collider other)
    {
        if (platform == null) return;
        if (PersistenceManager.IsRestoring) return;
        
        float now = Time.time;
        
        // Handle rigidbodies first to check for immunity
        if (!onlyAffectKCC)
        {
            var rb = other.attachedRigidbody ?? other.GetComponentInParent<Rigidbody>();
            if (rb != null && rb != platform.GetComponent<Rigidbody>() && !IsPlayerRelatedRigidbody(rb))
            {
                // Skip if item has drop immunity
                if (InvItemDropping.IsItemDropImmune(rb.gameObject))
                {
                    return;
                }
                
                if (!_passengers.ContainsKey(rb))
                {
                    RegisterRigidbody(rb);
                }

                if (_passengers.ContainsKey(rb))
                {
                    _passengers[rb].lastSeenTime = now;
                    
                    if (parentItemsToPlatform && !_passengers[rb].isParented && ShouldParentItem(rb) && platform.IsMoving)
                    {
                        ParentToPlatform(rb, _passengers[rb]);
                    }
                    else if (_passengers[rb].isParented && !platform.IsMoving)
                    {
                        UnparentFromPlatform(rb, _passengers[rb]);
                    }
                }
            }
        }
        
        // Handle motors
        var motor = other.GetComponentInParent<KinematicCharacterMotor>();
        if (motor != null && !_passengers.ContainsKey(motor))
        {
            RegisterMotor(motor);
        }
        if (motor != null && _passengers.ContainsKey(motor))
        {
            _passengers[motor].lastSeenTime = now;
            
            if (forceAttachmentAtHighSpeed && IsMovingFast())
            {
                var platformRb = platform.GetComponent<Rigidbody>();
                if (motor.AttachedRigidbodyOverride != platformRb)
                {
                    motor.AttachedRigidbodyOverride = platformRb;
                }
            }
        }
    }

    private bool IsPlayerRelatedRigidbody(Rigidbody rb)
    {
        if (rb == null) return false;
        return rb.GetComponentInParent<KinematicCharacterMotor>() != null;
    }

    private void OnTriggerExit(Collider other)
    {
        var motor = other.GetComponentInParent<KinematicCharacterMotor>();
        if (motor != null && _passengers.ContainsKey(motor))
        {
            _passengers[motor].lastSeenTime = Time.time;
        }
        
        var rb = other.attachedRigidbody ?? other.GetComponentInParent<Rigidbody>();
        if (rb != null && _passengers.ContainsKey(rb))
        {
            _passengers[rb].lastSeenTime = Time.time;
        }
    }

    private void Update()
    {
        if (platform == null) return;
        if (_passengers.Count == 0) return;
        if (PersistenceManager.IsRestoring) return;
        
        float now = Time.time;
        _toRemove.Clear();
        
        foreach (var kvp in _passengers)
        {
            var info = kvp.Value;
            if (info.component == null)
            {
                _toRemove.Add(kvp.Key);
                continue;
            }
            
            // Check if rigidbody has gained drop immunity (shouldn't happen but be safe)
            if (info.component is Rigidbody rb)
            {
                if (InvItemDropping.IsItemDropImmune(rb.gameObject))
                {
                    _toRemove.Add(kvp.Key);
                    continue;
                }
            }
            
            float gracePeriod = CalculateGracePeriod(info.component);
            
            if (now - info.lastSeenTime > gracePeriod)
            {
                _toRemove.Add(kvp.Key);
            }
        }
        
        foreach (var comp in _toRemove)
        {
            RemovePassenger(comp);
        }
    }

    private float CalculateGracePeriod(Component passenger)
    {
        float grace = detachGraceSeconds;
        
        if (IsMovingFast())
        {
            grace += highSpeedExtraGrace;
        }
        
        if (scaleGraceWithSpeed && _platformMover != null)
        {
            float platformSpeed = _platformMover.Velocity.magnitude;
            grace += (platformSpeed / 100f) * gracePerHundredSpeed;
        }

        if (useDynamicGrace && _platformMover != null)
        {
            float platformSpeed = _platformMover.Velocity.magnitude;
            grace += platformSpeed * velocityGraceMultiplier;
            
            if (passenger is KinematicCharacterMotor motor)
            {
                grace += motor.Velocity.magnitude * velocityGraceMultiplier * 0.5f;
            }
            else if (passenger is Rigidbody rb && !rb.isKinematic)
            {
                grace += rb.linearVelocity.magnitude * velocityGraceMultiplier * 0.5f;
            }
        }
        
        return Mathf.Min(grace, maxGraceTime);
    }

    private void RemovePassenger(Component comp)
    {
        if (!_passengers.TryGetValue(comp, out var info)) return;
        
        if (comp is KinematicCharacterMotor motor)
        {
            platform.UnregisterPassenger(motor);
            
            if (motor.AttachedRigidbodyOverride == platform.GetComponent<Rigidbody>())
            {
                motor.AttachedRigidbodyOverride = null;
            }
        }
        else if (comp is Rigidbody rb)
        {
            if (info.isParented && rb != null)
            {
                UnparentFromPlatform(rb, info);
            }
            else if (rb != null)
            {
				if (IsSmallItem(rb) || IsHeavyObject(rb))
                {
                    TogglePlayerCollisionWithItem(rb, info, false);
                    SetItemCollidersAsTrigger(rb, info, false);
                }
            }
            
            platform.UnregisterPassengerRigidbody(rb);
        }
        
        _passengers.Remove(comp);
    }

    private bool IsMovingFast()
    {
        if (_platformMover == null) return false;
        return _platformMover.Velocity.magnitude > highSpeedThreshold;
    }

    public void SetPaused(bool paused)
    {
        _isPaused = paused;
        
        if (paused)
        {
            if (platform != null)
            {
                platform.SetSleepMode(true);
            }
        }
        else
        {
            if (platform != null)
            {
                platform.SetSleepMode(false);
            }
        }
    }

    private void LateUpdate()
    {
        if (!PersistenceManager.IsRestoring)
        {
            return;
        }
        if (_passengers.Count == 0) return;

        foreach (var kv in _passengers)
        {
            if (kv.Key is KinematicCharacterMotor motor)
            {
                motor.BaseVelocity = Vector3.zero;
                var rb = platform != null ? platform.GetComponent<Rigidbody>() : null;
                if (rb != null && motor.AttachedRigidbodyOverride != rb)
                {
                    motor.AttachedRigidbodyOverride = rb;
                }
            }
            else if (kv.Key is Rigidbody rb)
            {
                if (!rb.isKinematic)
                {
                    rb.linearVelocity = Vector3.zero;
                    rb.angularVelocity = Vector3.zero;
                }
				if (_passengers.TryGetValue(rb, out var pInfo) && pInfo.isParented && (IsSmallItem(rb) || IsHeavyObject(rb)))
                {
                    TogglePlayerCollisionWithItem(rb, pInfo, true);
                }
            }
        }
    }

    private void OnDestroy()
    {
        _toRemove.Clear();
        _toRemove.AddRange(_passengers.Keys);
        
        foreach (var comp in _toRemove)
        {
            RemovePassenger(comp);
        }
        
        _passengers.Clear();
    }

    private void OnDrawGizmosSelected()
    {
        if (_collider == null) return;
        
        Gizmos.color = new Color(0, 1, 0, 0.3f);
        
        if (_collider is BoxCollider box)
        {
            Matrix4x4 oldMatrix = Gizmos.matrix;
            Gizmos.matrix = Matrix4x4.TRS(transform.position, transform.rotation, transform.lossyScale);
            Gizmos.DrawCube(box.center, box.size);
            Gizmos.color = Color.green;
            Gizmos.DrawWireCube(box.center, box.size);
            Gizmos.matrix = oldMatrix;
        }
        
        if (Application.isPlaying && _passengers.Count > 0)
        {
            Gizmos.color = Color.yellow;
            foreach (var info in _passengers.Values)
            {
                if (info.component != null)
                {
                    Gizmos.DrawLine(transform.position, info.component.transform.position);
                }
            }
        }
    }

    private void TogglePlayerCollisionWithItem(Rigidbody rb, PassengerInfo info, bool ignore)
    {
        if (_playerCapsule == null || rb == null)
        {
            return;
        }

        if (info.cachedColliders == null || info.cachedColliders.Count == 0)
        {
            info.cachedColliders = new List<Collider>(rb.GetComponentsInChildren<Collider>(true));
        }
        if (info.cachedColliders == null)
        {
            return;
        }

        if (ignore && info.collisionsIgnoredWithPlayer) return;
        if (!ignore && !info.collisionsIgnoredWithPlayer) return;

        for (int i = 0; i < info.cachedColliders.Count; i++)
        {
            var col = info.cachedColliders[i];
            if (col == null) continue;
            if (col.isTrigger) continue;
            Physics.IgnoreCollision(_playerCapsule, col, ignore);
        }

        info.collisionsIgnoredWithPlayer = ignore;
    }

    private bool IsSmallItem(Rigidbody rb)
    {
        return rb != null && rb.GetComponent<InvItemPickup>() != null;
    }

    private bool IsHeavyObject(Rigidbody rb)
    {
        return rb != null && rb.GetComponent<HeavyObjectManager>() != null;
    }

    private void SetItemCollidersAsTrigger(Rigidbody rb, PassengerInfo info, bool asTrigger)
    {
        if (rb == null) return;

        if (info.cachedColliders == null || info.cachedColliders.Count == 0)
        {
            info.cachedColliders = new List<Collider>(rb.GetComponentsInChildren<Collider>(true));
        }
        if (info.cachedColliders == null || info.cachedColliders.Count == 0) return;

        if (asTrigger)
        {
            if (info.originalIsTrigger == null || info.originalIsTrigger.Count != info.cachedColliders.Count)
            {
                info.originalIsTrigger = new List<bool>(info.cachedColliders.Count);
                info.originalEnabled = new List<bool>(info.cachedColliders.Count);
                info.originalIsTrigger.Clear();
                info.originalEnabled.Clear();
                for (int i = 0; i < info.cachedColliders.Count; i++)
                {
                    var col = info.cachedColliders[i];
                    if (col == null)
                    {
                        info.originalIsTrigger.Add(true);
                        info.originalEnabled.Add(false);
                        continue;
                    }
                    info.originalIsTrigger.Add(col.isTrigger);
                    info.originalEnabled.Add(col.enabled);
                }
            }

            for (int i = 0; i < info.cachedColliders.Count; i++)
            {
                var col = info.cachedColliders[i];
                if (col == null) continue;
                col.isTrigger = true;
            }
        }
        else
        {
            if (info.originalIsTrigger != null && info.originalEnabled != null &&
                info.originalIsTrigger.Count == info.cachedColliders.Count &&
                info.originalEnabled.Count == info.cachedColliders.Count)
            {
                for (int i = 0; i < info.cachedColliders.Count; i++)
                {
                    var col = info.cachedColliders[i];
                    if (col == null) continue;
                    col.isTrigger = info.originalIsTrigger[i];
                    col.enabled = info.originalEnabled[i];
                }
            }
        }
    }
}