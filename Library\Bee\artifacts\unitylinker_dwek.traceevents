{ "pid": 160076, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "UnityLinker" } },
{ "pid": 160076, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "0" } },
{ "pid": 160076, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 160076, "tid": 1, "ts": 1755645959176061, "dur": 1743161, "ph": "X", "name": "UnityLinker.exe", "args": {"analytics": "1"} },
{ "pid": 160076, "tid": 1, "ts": 1755645959177542, "dur": 379302, "ph": "X", "name": "InitAndSetup", "args": {} },
{ "pid": 160076, "tid": 1, "ts": 1755645959510484, "dur": 45710, "ph": "X", "name": "PrepareInstances", "args": {} },
{ "pid": 160076, "tid": 1, "ts": 1755645959608919, "dur": 14552, "ph": "X", "name": "ParseArguments", "args": {} },
{ "pid": 160076, "tid": 1, "ts": 1755645959624566, "dur": 161764, "ph": "X", "name": "CopyModeStep", "args": {} },
{ "pid": 160076, "tid": 1, "ts": 1755645959786386, "dur": 882554, "ph": "X", "name": "LoadReferencesStep", "args": {} },
{ "pid": 160076, "tid": 1, "ts": 1755645960668964, "dur": 236056, "ph": "X", "name": "UnityOutputStep", "args": {} },
{ "pid": 160076, "tid": 1, "ts": 1755645960915482, "dur": 3593, "ph": "X", "name": "Analytics", "args": {} },
{ "pid": 160076, "tid": 1, "ts": 1755645960919225, "dur": 329, "ph": "X", "name": "UnregisterRuntimeEventListeners", "args": {} },
{ "pid": 160076, "tid": 1, "ts": 1755645960930776, "dur": 2617, "ph": "X", "name": "", "args": {} },
{ "pid": 160076, "tid": 1, "ts": 1755645960929251, "dur": 4660, "ph": "X", "name": "Write chrome-trace events", "args": {} },
